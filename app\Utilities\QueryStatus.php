<?php

namespace App\Utilities;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;

class QueryStatus
{
    /**
     * @param $data
     * @param $message
     * @return false|JsonResponse
     */
    public static function check_empty($data, $message): bool|JsonResponse
    {
        if (($data instanceof Collection && !$data->count()) || (is_array($data) && empty($data))) {
            return response()->json([RequestStatus::EMPTY => $message], Response::HTTP_OK);
        }

        return false;
    }

    /**
     * @param $data
     * @param $message
     * @return false|JsonResponse
     */
    public static function check_found($data, $message): bool|JsonResponse
    {
        if(!$data){
            return response()->json([RequestStatus::NOTFOUND => $message], Response::HTTP_NOT_FOUND);
        }

        return false;
    }
}
