import ApplicationLogo from '@/Components/ApplicationLogo';
import { <PERSON> } from '@inertiajs/react';
import { PropsWithChildren } from 'react';

export default function Guest({ children }: PropsWithChildren) {
    return (
        <div className="flex min-h-screen flex-col items-center bg-gray-100 pt-6 sm:justify-center sm:pt-0">
            <div>
                <Link href="/">
                    <ApplicationLogo className="w-36" />
                </Link>
            </div>

            <div className="w-full overflow-hidden shadow-md sm:max-w-md">
                {children}
            </div>
        </div>
    );
}
