<?php

use Inertia\Inertia;
use Illuminate\Support\Facades\Route;
use Illuminate\Foundation\Application;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\RevenueController;
use App\Http\Controllers\Dashboard\SettingsController;
use App\Http\Controllers\Dashboard\DashboardController;
use App\Http\Controllers\Dashboard\FinancialController;
use App\Http\Controllers\Dashboard\HelpCenterController;
use App\Http\Controllers\Dashboard\NotificationController;
use App\Http\Controllers\Dashboard\Bookings\BookingController;
use App\Http\Controllers\Dashboard\Apartments\ApartmentsController;

Route::redirect('/', '/dashboard');

Route::middleware('auth:admin')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/properties', [ApartmentsController::class, 'index'])->name('properties');
    Route::get('/bookings', [BookingController::class, 'index'])->name('bookings');
    Route::get('/users/apartment-owners', [UserController::class, 'owners'])->name('users.owner');
    Route::get('/users/customer-accounts', [UserController::class, 'customers'])->name('users.customer');
    Route::get('/financials', [FinancialController::class, 'index'])->name('financials');
    Route::get('/revenues/overview', [RevenueController::class, 'overview'])->name('revenues.overview');
    Route::get('/revenues/user-analytics', [RevenueController::class, 'userAnalytics'])->name('revenues.analytics');
    Route::get('/revenues/performance-metrics', [RevenueController::class, 'performanceMetrics'])->name('revenues.performance');
    Route::get('/notifications', [NotificationController::class, 'index'])->name('notifications');
    Route::get('/help-center', [HelpCenterController::class, 'index'])->name('helpCenter');
    Route::get('/settings', [SettingsController::class, 'index'])->name('settings');
    Route::post('/settings/countries/bulk-suspend', [SettingsController::class, 'bulkSuspend'])->name('settings.countries.bulk-suspend');
    Route::post('/settings/countries/{id}/suspend', [SettingsController::class, 'suspendCountry'])->name('settings.countries.suspend');
    Route::post('/settings/countries/{id}/restore', [SettingsController::class, 'restoreCountry'])->name('settings.countries.restore');
});

Route::middleware('auth:admin')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
