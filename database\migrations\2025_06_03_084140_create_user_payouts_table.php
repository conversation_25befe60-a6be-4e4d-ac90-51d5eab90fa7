<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_payouts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id');
            $table->foreignId('booking_id');
            $table->foreignId('apartment_id');
            $table->string('amount');
            $table->json('bank_information');
            $table->string('status')->default('pending');
            $table->string('code')->unique();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_payouts');
    }
};
