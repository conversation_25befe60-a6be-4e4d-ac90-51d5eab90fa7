<?php

namespace App\Utilities;

use Carbon\Carbon;
use DatePeriod;
use DateTime;
use DateInterval;

class DateManipulator
{
    /**
     * @param $date
     * @return mixed
     */
    public static function time_remaining($date)
    {
        $now = new DateTime();

        $future_date = new DateTime($date);

        $interval = $future_date->diff($now);

        return $interval->format("%a days, %h hours, %i minutes, %s seconds");
    }

    /**
     * @param $datetime
     * @param false $full
     * @return string
     * @throws \Exception
     */
    public static function time_ago($datetime, $full = false)
    {
        $now = new DateTime;
        $ago = new DateTime($datetime);
        $diff = $now->diff($ago);

        $diff->w = floor($diff->d / 7);
        $diff->d -= $diff->w * 7;

        $string = array(
            'y' => 'year',
            'm' => 'month',
            'w' => 'week',
            'd' => 'day',
            'h' => 'hour',
            'i' => 'minute',
            's' => 'second',
        );
        foreach ($string as $k => &$v) {
            if ($diff->$k) {
                $v = $diff->$k . ' ' . $v . ($diff->$k > 1 ? 's' : '');
            } else {
                unset($string[$k]);
            }
        }

        if(!$full){
            $string = array_slice($string, 0, 1);
        }

        return $string ? implode(', ', $string) . ' ago' : 'just now';
    }

    /**
     * @param $date
     * @return string
     */
    public static function age($date)
    {
        if($date == null){
            return 'Date of birth not provided';
        }

        $birth_date = date('Y-m-d', strtotime($date));

        $year = date("Y") - date("Y", strtotime($birth_date));

        if($year > 0){
            $suffix = $year == 1 ? ' old' : 's old';
            return $year . ' year'.$suffix;
        }
        else{
            $month = date("m") - date("m", strtotime($birth_date));

            if($month > 0){
                $suffix = $month == 1 ? ' old' : 's old';
                return $month . ' month'.$suffix;
            }
            else{
                $day = date("d") - date("d", strtotime($birth_date));

                if($day > 0){
                    $suffix = $day == 1 ? ' old' : 's old';
                    return $day . ' day'.$suffix;
                }
                else{
                    return "Few hours old";
                }
            }
        }
    }

    /**
     * @param $datetime1
     * @param $datetime2
     * @return float
     */
    public static function date_percent_diff($datetime1, $datetime2)
    {
        $start = Carbon::parse($datetime1)->timestamp;

        $end = Carbon::parse($datetime2)->timestamp;

        $timespan = $end - $start;

        $current = Carbon::now()->timestamp - $start;

        $progress = $current / $timespan;

        $remaining = (1 - $progress) * 100;

        return round($remaining, 2);
    }

    /**
     * @param $date
     * @return false|int|string
     *
     */
    public static function get_age($date)
    {
        $birthDate = date('m/d/Y', strtotime($date));
        $birthDate = explode("/", $birthDate);

        $age = (date("md", date("U", mktime(0, 0, 0, $birthDate[0], $birthDate[1], $birthDate[2]))) > date("md")
            ? ((date("Y") - $birthDate[2]) - 1)
            : (date("Y") - $birthDate[2]));

        return $age;
    }

    /**
     * @param $start
     * @param $end
     * @param string $format
     * @return array
     */
    public static function getDatesFromRange($start, $end, $format = 'Y-m-d')
    {
        // Declare an empty array
        $array = array();

        // Variable that store the date interval
        // of period 1 day
        try {
            $interval = new DateInterval('P1D');
        }
        catch (\Exception $e) {
        }

        $realEnd = new DateTime($end);
        $realEnd->add($interval);

        $period = new DatePeriod(new DateTime($start), $interval, $realEnd);

        // Use loop to store date into array
        foreach ($period as $date)
        {
            $array[] = $date->format($format);
        }

        // Return the array elements
        return $array;
    }

    /**
     * @param $startDate
     * @param $endDate
     * @return int
     */
    public static function getWorkingDays($startDate, $endDate)
    {
        $begin = strtotime($startDate);
        $end   = strtotime($endDate);
        if ($begin > $end) {
            return 0;
        }
        else {
            $no_days = 0;
            while ($begin <= $end) {
                $what_day = date("N", $begin);
                if(!in_array($what_day, [6,7])) // 6 and 7 are weekend
                    $no_days++;
                $begin += 86400; // +1 day
            };

            return $no_days;
        }
    }

    /**
     * @param $date
     * @return array
     */
    public static function listWorkingDays($date)
    {
        $workdays = array();
        $type = CAL_GREGORIAN;
        $month = date('n', strtotime($date)); // Month ID, 1 through to 12.
        $year = date('Y', strtotime($date)); // Year in 4 digit 2009 format.
        $day_count = cal_days_in_month($type, $month, $year); // Get the amount of days

        //loop through all days
        for ($i = 1; $i <= $day_count; $i++) {

            $date = $year.'/'.$month.'/'.$i; //format date
            $get_name = date('l', strtotime($date)); //get week day
            $day_name = substr($get_name, 0, 3); // Trim day name to 3 chars

            //if not a weekend add day to array
            if($day_name != 'Sun' && $day_name != 'Sat'){
                $workdays[] = date('Y-m-d', strtotime($year.'-'.$month.'-'.$i));
            }

        }

        return $workdays;
    }

    /**
     * @param $start
     * @param $end
     * @param string $format
     * @return array
     */
    public static function getWorkingDaysFromRange($start, $end, $format = 'Y-m-d')
    {
        // Declare an empty array
        $array = array();

        // Variable that store the date interval
        // of period 1 day
        try {
            $interval = new DateInterval('P1D');
        }
        catch (\Exception $e) {
        }

        $realEnd = new DateTime($end);
        $realEnd->add($interval);

        $period = new DatePeriod(new DateTime($start), $interval, $realEnd);

        // Use loop to store date into array
        foreach ($period as $date)
        {
            $day_name = Carbon::parse($date)->getTranslatedDayName();
            if($day_name != 'Sunday' && $day_name != 'Saturday') {
                $array[] = $date->format($format);
            }
        }

        // Return the array elements
        return $array;
    }

    /**
     * @param $currentDate
     * @param $numBusDays
     * @param array $holidays
     * @param array $resultDates
     * @return array|mixed
     */
    public function getWorkingDaysFromCount($currentDate, $numBusDays, $holidays = array(), $resultDates = array())
    {
        //  exit when we have collected the required number of business days
        if ($numBusDays === 0) {
            return $resultDates;
        }

        //  add current date to return array, if not a weekend or holiday
        $date = date("w", strtotime($currentDate));
        if ( $date != 0  &&  $date != 6  &&  !in_array($currentDate, $holidays) ) {
            $resultDates[] = $currentDate;
            $numBusDays -= 1;
        }

        //  set up the next date to test
        $currentDate = new DateTime("$currentDate + 1 day");
        $currentDate = $currentDate->format('Y-m-d H:i:s');

        return $this->getWorkingDaysFromCount($currentDate, $numBusDays, $holidays, $resultDates);
    }

    /**
     * @param $start
     * @param $end
     * @param string $format
     * @return int
     */
    public static function countDatesFromRange($start, $end, $format = 'Y-m-d')
    {
        $checkinTime = (int) date('H', strtotime($start)); // Extract hour from check-in time
        $checkinDate = date('Y-m-d', strtotime($start));
        $checkoutDate = date('Y-m-d', strtotime($end));

        // Base nights count
        $nights = (strtotime($checkoutDate) - strtotime($checkinDate)) / (60 * 60 * 24);

        // If checked in between 12:01 AM - 6:00 AM, first night is the same day
        if ($checkinTime < 6) {
            $nights -= 1; // Reduce one night since they checked in early but counted extra
        }

        return max(1, $nights); // Ensure minimum stay is 1 night
    }
}
