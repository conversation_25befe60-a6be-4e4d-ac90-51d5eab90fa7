<?php

namespace App\Services\Email;

class EmailSender
{
    /**
     * @param string $content
     * @return string
     */
    private function baseTemplate(string $content): string
    {
        return <<<HTML
            <!DOCTYPE html>
            <html>
            <head>
                <meta http-equiv="Content-Type" content="text/html"; charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Easy Foods NG!</title>
                <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">
                <style type="text/css">
                    body {
                        margin: 0;
                        padding: 0;
                        font-family: "Open Sans", Arial, sans-serif;
                        color: #333333;
                        line-height: 1.5;
                        -webkit-font-smoothing: antialiased;
                        -webkit-text-size-adjust: none;
                        width: 100% !important;
                    }

                    .email-container {
                        max-width: 800px;
                        margin: 0 auto;
                        font-family: "Open Sans", Arial, sans-serif;
                    }

                    .header {
                        background: lightgreen;
                        padding: 20px 0;
                        text-align: center;
                        font-family: "Open Sans", Arial, sans-serif;
                    }

                    .logo {
                        max-width: 80px;
                        height: auto;
                        font-family: "Open Sans", Arial, sans-serif;
                    }

                    .email-body {
                        border: 1px solid #eeeeee;
                        border-radius: 8px;
                        overflow: hidden;
                        background-color: #ffffff;
                        font-family: "Open Sans", Arial, sans-serif;
                    }

                    .content {
                        padding: 24px 32px;
                        font-family: "Open Sans", Arial, sans-serif;
                    }

                    .footer {
                        padding: 24px 10px;
                        color: #a3abb4;
                        font-size: 11px;
                        text-align: center;
                        font-family: "Open Sans", Arial, sans-serif;
                    }

                    .inner-footer {
                        padding: 0 32px 24px;
                        border-top: 1px solid #e4e4e9;
                        font-family: "Open Sans", Arial, sans-serif;
                    }

                    .signature {
                        font-size: 12px;
                        line-height: 1.5;
                        font-family: "Open Sans", Arial, sans-serif;
                    }

                    .code {
                        font-size: 24px;
                        font-weight: bold;
                        letter-spacing: 3px;
                        text-align: center;
                        margin: 20px 0;
                        padding: 10px;
                        font-family: "Open Sans", Arial, sans-serif;
                    }

                    .button {
                        background-color: #4CAF50;
                        color: white;
                        padding: 12px 24px;
                        text-decoration: none;
                        border-radius: 4px;
                        display: inline-block;
                        font-weight: 600;
                        margin: 15px 0;
                        font-family: "Open Sans", Arial, sans-serif;
                    }

                    p {
                        font-size: 14px;
                        margin: 0 0 10px 0;
                        font-family: "Open Sans", Arial, sans-serif;
                    }

                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 15px 0;
                        font-family: "Open Sans", Arial, sans-serif;
                    }

                    th, td {
                        padding: 10px;
                        text-align: left;
                        border-bottom: 1px solid #eeeeee;
                        font-size: 14px;
                        font-family: "Open Sans", Arial, sans-serif;
                    }

                    th {
                        font-weight: 600;
                        font-family: "Open Sans", Arial, sans-serif;
                    }

                    .text-center {
                        text-align: center;
                        font-family: "Open Sans", Arial, sans-serif;
                    }

                    h1, h2, h3, h4, h5, h6 {
                        font-family: "Open Sans", Arial, sans-serif;
                    }
                </style>
            </head>
            <body>
                <center class="email-container">
                    <div class="header">
                        <img src="https://api.easyfoods.ng/images/logo-dark.png" alt="Easy Foods" class="logo" style="max-width:80px; height:auto;">
                    </div>
                    <table class="email-body" width="100%" cellpadding="0" cellspacing="0">
                        <tr>
                            <td class="content">

                                $content

                            </td>
                        </tr>
                        <tr>
                            <td class="inner-footer">
                                <table width="100%" cellpadding="0" cellspacing="0">
                                    <tr>
                                        <td class="signature">
                                            <span>Sincerely,</span><br><br>
                                            <strong>Easy Foods NG! Team</strong><br>
                                            <span style="color:#a3abb4;font-size:10px"><a href="mailto:<EMAIL>" style="color:#a3abb4;text-decoration:none;"><EMAIL></a></span>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                    <div class="footer">
                        Easy Foods NG!<br>

                        <a href="#" style="color:#a3abb4;text-decoration:none;">Privacy Policy</a> |
                        <a href="#" style="color:#a3abb4;text-decoration:none;">Terms Of Service</a>
                    </div>
                </center>
            </body>
        </html>
        HTML;
    }

    /**
     * Welcome email template
     * @param array $args - ['name' => string, 'login_url' => string]
     * @return string HTML
     */
    public function welcomeEmail(array $args): string
    {
        $name = $args['name'];

        $content = <<<HTML
            <h2 style="padding-bottom:10px;margin:0">Hi $name,</h2>
            <p style="font-size:14px;padding-bottom:10px;margin:0">Welcome to Easy Foods! We are excited to have you on board.</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">If you have any questions, feel free to contact our support team.</p>
        HTML;

        return $this->baseTemplate($content);
    }

    /**
     * Email verification code template
     * @param array $args - ['name' => string, 'code' => string, 'expiry' => string]
     * @return string HTML
     */
    public function emailVerificationCode(array $args): string
    {
        $name = $args['name'] ?? 'User';
        $code = $args['code'] ?? '123456';
        $expiry = $args['expiry'] ?? '15 minutes';

        $content = <<<HTML
            <h2 style="padding-bottom:10px;margin:0">Hi $name,</h2>
            <p style="font-size:14px;padding-bottom:10px;margin:0">Thank you for registering with us. Please use the following verification code:</p>
            <h1 style="font-size: 50px;font-weight:bold;padding:15px 0;margin:0;text-align:center;letter-spacing:3px;">$code</h1>
            <p style="font-size:14px;padding-bottom:10px;margin:0">This code will expire in $expiry. Please do not share this code with anyone.</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">If you did not request this code, you can safely ignore this email.</p>
        HTML;

        return $this->baseTemplate($content);
    }

    /**
     * @param array $args
     * @return string
     */
    public function passwordResetCode(array $args): string
    {
        $name = $args['name'] ?? 'User';
        $code = $args['code'] ?? '123456';
        $expiry = $args['expiry'] ?? '15 minutes';

        // $text1 = $this->translator->translate('forgottenPassword');
        // $text2 = $this->translator->translate('codeWillExpire', ['expiry' => $expiry]);
        // $text3 = $this->translator->translate('ignoreEmail');
        $text1 = ('forgottenPassword');
        // $text2 = ('codeWillExpire', ['expiry' => $expiry]);
        $text2 = ('codeWillExpire');
        $text3 = ('ignoreEmail');

        $content = <<<HTML
            <h2 style="padding-bottom:10px;margin:0">Hi $name,</h2>
            <p style="font-size:14px;padding-bottom:10px;margin:0">$text1</p>
            <h1 style="font-size: 50px;font-weight:bold;padding:15px 0;margin:0;text-align:center;letter-spacing:3px;">$code</h1>
            <p style="font-size:14px;padding-bottom:10px;margin:0">$text2</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">$text3</p>
        HTML;

        return $this->baseTemplate($content);
    }

    /**
     * @param array $args
     * @return string
     */
    public function orderPlaced(array $args): string
    {
        $name = $args['name'] ?? 'Customer';
        $orderId = $args['order_id'] ?? 'N/A';
        $orderDate = $args['order_date'] ?? date('Y-m-d');
        $items = $args['items'] ?? [];
        $total = $args['total'] ?? '0.00';
        $trackingUrl = $args['tracking_url'] ?? '#';

        $itemsHtml = '';
        foreach ($items as $item) {
            $itemsHtml .= "<tr>
                <td style='border-bottom:1px solid #eee;padding:8px 0;font-size:14px;'>{$item['name']}</td>
                <td style='border-bottom:1px solid #eee;padding:8px 0;font-size:14px;text-align:right;'>{$item['quantity']}</td>
                <td style='border-bottom:1px solid #eee;padding:8px 0;font-size:14px;text-align:right;'>{$item['price']}</td>
                <td style='border-bottom:1px solid #eee;padding:8px 0;font-size:14px;text-align:right;'>{$item['total']}</td>
            </tr>";
        }

        $content = <<<HTML
            <p style="font-size:14px;padding-bottom:10px;margin:0">Hi $name,</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">Thank you for your order #$orderId placed on $orderDate.</p>
            <p style="font-size:14px;padding-bottom:15px;margin:0">Here's your order summary:</p>

            <table style="width:100%;border-collapse:collapse;margin-bottom:15px;">
                <thead>
                    <tr>
                        <th style="text-align:left;padding:8px 0;border-bottom:1px solid #eee;font-size:14px;font-weight:bold;">Item</th>
                        <th style="text-align:right;padding:8px 0;border-bottom:1px solid #eee;font-size:14px;font-weight:bold;">Qty</th>
                        <th style="text-align:right;padding:8px 0;border-bottom:1px solid #eee;font-size:14px;font-weight:bold;">Price</th>
                        <th style="text-align:right;padding:8px 0;border-bottom:1px solid #eee;font-size:14px;font-weight:bold;">Total</th>
                    </tr>
                </thead>
                <tbody>
                    $itemsHtml
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="3" style="text-align:right;padding:8px 0;font-size:14px;font-weight:bold;border-top:1px solid #eee;">Total:</td>
                        <td style="text-align:right;padding:8px 0;font-size:14px;font-weight:bold;border-top:1px solid #eee;">$total</td>
                    </tr>
                </tfoot>
            </table>

            <p style="font-size:14px;padding-bottom:10px;margin:0">You can track your order using the link below:</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">
                <a href="$trackingUrl" style="background-color:#2196F3;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;display:inline-block;margin-top:10px;">Track Your Order</a>
            </p>
        HTML;

        return $this->baseTemplate($content);
    }

    /**
     * Order received template
     * @param array $args - ['name' => string, 'order_id' => string]
     * @return string HTML
     */
    public static function orderReceived(array $args): string
    {
        $name = $args['name'] ?? 'Customer';
        $orderId = $args['order_id'] ?? 'N/A';

        $content = <<<HTML
            <p style="font-size:14px;padding-bottom:10px;margin:0">Hi $name,</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">We've received your order #$orderId and it's now being processed.</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">You'll receive another email once your order has been shipped.</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">Thank you for shopping with us!</p>
        HTML;

        return self::baseTemplate($content);
    }

    /**
     * Order delivered template
     * @param array $args - ['name' => string, 'order_id' => string, 'delivery_date' => string]
     * @return string HTML
     */
    public static function orderDelivered(array $args): string
    {
        $name = $args['name'] ?? 'Customer';
        $orderId = $args['order_id'] ?? 'N/A';
        $deliveryDate = $args['delivery_date'] ?? date('Y-m-d');

        $content = <<<HTML
            <p style="font-size:14px;padding-bottom:10px;margin:0">Hi $name,</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">Great news! Your order #$orderId was delivered on $deliveryDate.</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">We hope you're happy with your purchase. If you have any questions or need assistance, please don't hesitate to contact us.</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">Thank you for shopping with us!</p>
        HTML;

        return self::baseTemplate($content);
    }

    /**
     * Vendor account approved template
     * @param array $args - ['name' => string, 'login_url' => string]
     * @return string HTML
     */
    public function vendorAccountApproved(array $args): string
    {
        $name = $args['name'] ?? 'Vendor';
        $loginUrl = $args['login_url'] ?? '#';

        $content = <<<HTML
            <p style="font-size:14px;padding-bottom:10px;margin:0">Hi $name,</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">We're pleased to inform you that your vendor account has been approved!</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">You can now login to your vendor dashboard and start listing your products.</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">
                <a href="$loginUrl" style="background-color:#4CAF50;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;display:inline-block;margin-top:15px;">Access Vendor Dashboard</a>
            </p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">If you have any questions, please contact our vendor support team.</p>
        HTML;

        return $this->baseTemplate($content);
    }

    /**
     * Rider account approved template
     * @param array $args - ['name' => string, 'login_url' => string]
     * @return string HTML
     */
    public function riderAccountApproved(array $args): string
    {
        $name = $args['name'] ?? 'Rider';
        $loginUrl = $args['login_url'] ?? '#';

        $content = <<<HTML
            <p style="font-size:14px;padding-bottom:10px;margin:0">Hi $name,</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">Congratulations! Your rider account has been approved.</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">You can now login to the rider app and start accepting delivery requests.</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">
                <a href="$loginUrl" style="background-color:#4CAF50;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;display:inline-block;margin-top:15px;">Login to Rider App</a>
            </p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">If you need any assistance, please contact our rider support team.</p>
        HTML;

        return $this->baseTemplate($content);
    }

    /**
     * KYC approved template
     * @param array $args - ['name' => string]
     * @return string HTML
     */
    public function kycApproved(array $args): string
    {
        $name = $args['name'] ?? 'User';

        $content = <<<HTML
            <p style="font-size:14px;padding-bottom:10px;margin:0">Hi $name,</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">We're happy to inform you that your KYC verification has been successfully approved.</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">You now have full access to all platform features.</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">Thank you for completing the verification process.</p>
        HTML;

        return $this->baseTemplate($content);
    }

    /**
     * Forgotten password code template
     * @param array $args - ['name' => string, 'code' => string, 'expiry' => string]
     * @return string HTML
     */
    public function forgottenPasswordCode(array $args): string
    {
        $name = $args['name'] ?? 'User';
        $code = $args['code'] ?? '123456';
        $expiry = $args['expiry'] ?? '15 minutes';

        $content = <<<HTML
            <p style="font-size:14px;padding-bottom:10px;margin:0">Hi $name,</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">We received a request to reset your password. Please use the following verification code:</p>
            <p style="font-size:24px;font-weight:bold;padding:15px 0;margin:0;text-align:center;letter-spacing:3px;">$code</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">This code will expire in $expiry. Please do not share this code with anyone.</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">If you didn't request a password reset, you can safely ignore this email.</p>
        HTML;

        return $this->baseTemplate($content);
    }

    /**
     * Password reset success notification template
     * @param array $args - ['name' => string]
     * @return string HTML
     */
    public function resetPasswordSuccess(array $args): string
    {
        $name = $args['name'] ?? 'User';

        $content = <<<HTML
            <p style="font-size:14px;padding-bottom:10px;margin:0">Hi $name,</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">Your password has been successfully reset.</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">If you did not make this change, please contact our support team immediately.</p>
            <p style="font-size:14px;padding-bottom:10px;margin:0">For security reasons, we recommend reviewing your recent account activity.</p>
        HTML;

        return $this->baseTemplate($content);
    }
}
