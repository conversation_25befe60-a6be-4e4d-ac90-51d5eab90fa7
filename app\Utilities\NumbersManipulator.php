<?php

namespace App\Utilities;

class NumbersManipulator
{
    /**
     * @param $percentage
     * @param $number
     * @return float|int
     */
    public static function percentage($percentage, $number): float|int
    {
        if ($number == 0) {
            return 0;
        }

        return ($percentage / 100) * $number;
    }

    /**
     * @param $number
     * @return bool
     */
    public function is_negative($number): bool
    {
        return $number < 0;
    }
}
