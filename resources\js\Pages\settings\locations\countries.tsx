import { DataTable } from "@/Components/dataTable";
import { Checkbox } from "@/Components/ui/checkbox";
import { CountriesData, Country } from "@/types/models/country";
import { ColumnDef, PaginationState } from "@tanstack/react-table";
import { useMemo, useCallback } from "react";
import * as Flags from "country-flag-icons/react/3x2";
import { Badge } from "@/Components/ui/badge";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/Components/ui/dropdown-menu";
import { EllipsisVertical } from "lucide-react";
import { router } from "@inertiajs/react";

type Props = {
    countries: CountriesData;
    onSelectionChange?: (selectedRows: Country[]) => void;
    onSingleSuspend?: (country: Country) => void;
    onViewCountry?: (country: Country) => void;
};
export default function Countries({
    countries,
    onSelectionChange,
    onSingleSuspend,
    onViewCountry,
}: Props) {
    const handlePaginationChange = useCallback(
        (pagination: PaginationState) => {
            const currentUrl = new URL(window.location.href);
            const currentSearch = currentUrl.searchParams.get("search") || "";

            router.get(
                route("settings"),
                {
                    page: pagination.pageIndex + 1,
                    per_page: pagination.pageSize,
                    search: currentSearch, // Preserve current search
                },
                {
                    preserveState: true,
                    preserveScroll: true,
                }
            );
        },
        []
    );

    const columns: ColumnDef<Country>[] = useMemo(
        () => [
            {
                id: "select",
                header: ({ table }) => (
                    <Checkbox
                        checked={
                            table.getIsAllPageRowsSelected() ||
                            (table.getIsSomePageRowsSelected() &&
                                "indeterminate")
                        }
                        onCheckedChange={(value) =>
                            table.toggleAllPageRowsSelected(!!value)
                        }
                        aria-label="Select all"
                        className="w-4 h-4 border-gray-300"
                    />
                ),
                cell: ({ row }) => (
                    <Checkbox
                        checked={row.getIsSelected()}
                        onCheckedChange={(value) => row.toggleSelected(!!value)}
                        aria-label="Select row"
                        className="w-4 h-4 border-gray-300"
                    />
                ),
                enableSorting: false,
                enableHiding: false,
            },
            {
                header: "Country",
                accessorKey: "name",
                cell: ({ row }) => {
                    const FlagComponent =
                        Flags[row.original.iso as keyof typeof Flags];
                    return (
                        <div className="flex items-center gap-2">
                            {FlagComponent && (
                                <FlagComponent
                                    className="w-6 h-4"
                                    title={row.original.name}
                                />
                            )}
                            <p>{row.getValue("name")}</p>
                        </div>
                    );
                },
            },
            {
                header: "ISO Code",
                accessorKey: "iso",
            },
            {
                header: "No of States",
                id: "no_of_states",
                cell: () => <p>0</p>,
            },
            {
                header: "Currency",
                accessorKey: "currency",
            },
            {
                header: "Status",
                accessorKey: "deleted_at",
                cell: ({ row }) => {
                    const isSuspended = !!row.original.deleted_at;
                    return (
                        <Badge
                            variant={isSuspended ? "destructive" : "default"}
                            className={
                                isSuspended
                                    ? "bg-red-100 text-red-800"
                                    : "bg-green-100 text-green-800"
                            }
                        >
                            {isSuspended ? "Suspended" : "Active"}
                        </Badge>
                    );
                },
            },
            {
                header: "",
                accessorKey: "actions",
                cell: ({ row }) => {
                    const country = row.original;
                    const isSuspended = !!country.deleted_at;

                    return (
                        <DropdownMenu>
                            <DropdownMenuTrigger>
                                <EllipsisVertical className="h-5 w-5 cursor-pointer text-gray-600" />
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                                <DropdownMenuItem
                                    className="py-4 cursor-pointer"
                                    onClick={() => onViewCountry?.(country)}
                                >
                                    View Country
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                    className={`py-4 cursor-pointer ${
                                        isSuspended
                                            ? "text-green-600"
                                            : "text-red-600"
                                    }`}
                                    onClick={() => onSingleSuspend?.(country)}
                                >
                                    {isSuspended
                                        ? "Restore Country"
                                        : "Suspend Country"}
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    );
                },
            },
        ],
        []
    );

    return (
        <DataTable
            columns={columns}
            data={countries.data}
            rowCount={countries.total}
            manualPagination={true}
            paginationData={countries}
            onPaginationChange={handlePaginationChange}
            onSelectionChange={onSelectionChange}
        />
    );
}
