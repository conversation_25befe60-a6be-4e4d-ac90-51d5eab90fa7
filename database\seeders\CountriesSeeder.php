<?php

namespace Database\Seeders;

use App\Models\Country;
use Illuminate\Database\Seeder;

class CountriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing data
        Country::truncate();

        $countries = [
            ['id' => 1, 'name' => 'Afghanistan', 'iso' => 'AF', 'phonecode' => '93', 'currency' => 'AFN', 'flag' => '🇦🇫', 'code' => 'afghanistan'],
            ['id' => 2, 'name' => 'Albania', 'iso' => 'AL', 'phonecode' => '355', 'currency' => 'ALL', 'flag' => '🇦🇱', 'code' => 'albania'],
            ['id' => 3, 'name' => 'Algeria', 'iso' => 'DZ', 'phonecode' => '213', 'currency' => 'DZD', 'flag' => '🇩🇿', 'code' => 'algeria'],
            ['id' => 4, 'name' => 'Andorra', 'iso' => 'AD', 'phonecode' => '376', 'currency' => 'EUR', 'flag' => '🇦🇩', 'code' => 'andorra'],
            ['id' => 5, 'name' => 'Angola', 'iso' => 'AO', 'phonecode' => '244', 'currency' => 'AOA', 'flag' => '🇦🇴', 'code' => 'angola'],
            ['id' => 6, 'name' => 'Antigua and Barbuda', 'iso' => 'AG', 'phonecode' => '1268', 'currency' => 'XCD', 'flag' => '🇦🇬', 'code' => 'antigua-and-barbuda'],
            ['id' => 7, 'name' => 'Argentina', 'iso' => 'AR', 'phonecode' => '54', 'currency' => 'ARS', 'flag' => '🇦🇷', 'code' => 'argentina'],
            ['id' => 8, 'name' => 'Armenia', 'iso' => 'AM', 'phonecode' => '374', 'currency' => 'AMD', 'flag' => '🇦🇲', 'code' => 'armenia'],
            ['id' => 9, 'name' => 'Australia', 'iso' => 'AU', 'phonecode' => '61', 'currency' => 'AUD', 'flag' => '🇦🇺', 'code' => 'australia'],
            ['id' => 10, 'name' => 'Austria', 'iso' => 'AT', 'phonecode' => '43', 'currency' => 'EUR', 'flag' => '🇦🇹', 'code' => 'austria'],
            ['id' => 11, 'name' => 'Azerbaijan', 'iso' => 'AZ', 'phonecode' => '994', 'currency' => 'AZN', 'flag' => '🇦🇿', 'code' => 'azerbaijan'],
            ['id' => 12, 'name' => 'Bahamas', 'iso' => 'BS', 'phonecode' => '1242', 'currency' => 'BSD', 'flag' => '🇧🇸', 'code' => 'bahamas'],
            ['id' => 13, 'name' => 'Bahrain', 'iso' => 'BH', 'phonecode' => '973', 'currency' => 'BHD', 'flag' => '🇧🇭', 'code' => 'bahrain'],
            ['id' => 14, 'name' => 'Bangladesh', 'iso' => 'BD', 'phonecode' => '880', 'currency' => 'BDT', 'flag' => '🇧🇩', 'code' => 'bangladesh'],
            ['id' => 15, 'name' => 'Barbados', 'iso' => 'BB', 'phonecode' => '1246', 'currency' => 'BBD', 'flag' => '🇧🇧', 'code' => 'barbados'],
            ['id' => 16, 'name' => 'Belarus', 'iso' => 'BY', 'phonecode' => '375', 'currency' => 'BYN', 'flag' => '🇧🇾', 'code' => 'belarus'],
            ['id' => 17, 'name' => 'Belgium', 'iso' => 'BE', 'phonecode' => '32', 'currency' => 'EUR', 'flag' => '🇧🇪', 'code' => 'belgium'],
            ['id' => 18, 'name' => 'Belize', 'iso' => 'BZ', 'phonecode' => '501', 'currency' => 'BZD', 'flag' => '🇧🇿', 'code' => 'belize'],
            ['id' => 19, 'name' => 'Benin', 'iso' => 'BJ', 'phonecode' => '229', 'currency' => 'XOF', 'flag' => '🇧🇯', 'code' => 'benin'],
            ['id' => 20, 'name' => 'Bhutan', 'iso' => 'BT', 'phonecode' => '975', 'currency' => 'BTN', 'flag' => '🇧🇹', 'code' => 'bhutan'],
            ['id' => 21, 'name' => 'Bolivia', 'iso' => 'BO', 'phonecode' => '591', 'currency' => 'BOB', 'flag' => '🇧🇴', 'code' => 'bolivia'],
            ['id' => 22, 'name' => 'Bosnia and Herzegovina', 'iso' => 'BA', 'phonecode' => '387', 'currency' => 'BAM', 'flag' => '🇧🇦', 'code' => 'bosnia-and-herzegovina'],
            ['id' => 23, 'name' => 'Botswana', 'iso' => 'BW', 'phonecode' => '267', 'currency' => 'BWP', 'flag' => '🇧🇼', 'code' => 'botswana'],
            ['id' => 24, 'name' => 'Brazil', 'iso' => 'BR', 'phonecode' => '55', 'currency' => 'BRL', 'flag' => '🇧🇷', 'code' => 'brazil'],
            ['id' => 25, 'name' => 'Brunei Darussalam', 'iso' => 'BN', 'phonecode' => '673', 'currency' => 'BND', 'flag' => '🇧🇳', 'code' => 'brunei-darussalam'],
            ['id' => 26, 'name' => 'Bulgaria', 'iso' => 'BG', 'phonecode' => '359', 'currency' => 'BGN', 'flag' => '🇧🇬', 'code' => 'bulgaria'],
            ['id' => 27, 'name' => 'Burkina Faso', 'iso' => 'BF', 'phonecode' => '226', 'currency' => 'XOF', 'flag' => '🇧🇫', 'code' => 'burkina-faso'],
            ['id' => 28, 'name' => 'Burundi', 'iso' => 'BI', 'phonecode' => '257', 'currency' => 'BIF', 'flag' => '🇧🇮', 'code' => 'burundi'],
            ['id' => 29, 'name' => 'Cabo Verde', 'iso' => 'CV', 'phonecode' => '238', 'currency' => 'CVE', 'flag' => '🇨🇻', 'code' => 'cabo-verde'],
            ['id' => 30, 'name' => 'Cambodia', 'iso' => 'KH', 'phonecode' => '855', 'currency' => 'KHR', 'flag' => '🇰🇭', 'code' => 'cambodia'],
            ['id' => 31, 'name' => 'Cameroon', 'iso' => 'CM', 'phonecode' => '237', 'currency' => 'XAF', 'flag' => '🇨🇲', 'code' => 'cameroon'],
            ['id' => 32, 'name' => 'Canada', 'iso' => 'CA', 'phonecode' => '1', 'currency' => 'CAD', 'flag' => '🇨🇦', 'code' => 'canada'],
            ['id' => 33, 'name' => 'Central African Republic', 'iso' => 'CF', 'phonecode' => '236', 'currency' => 'XAF', 'flag' => '🇨🇫', 'code' => 'central-african-republic'],
            ['id' => 34, 'name' => 'Chad', 'iso' => 'TD', 'phonecode' => '235', 'currency' => 'XAF', 'flag' => '🇹🇩', 'code' => 'chad'],
            ['id' => 35, 'name' => 'Chile', 'iso' => 'CL', 'phonecode' => '56', 'currency' => 'CLP', 'flag' => '🇨🇱', 'code' => 'chile'],
            ['id' => 36, 'name' => 'China', 'iso' => 'CN', 'phonecode' => '86', 'currency' => 'CNY', 'flag' => '🇨🇳', 'code' => 'china'],
            ['id' => 37, 'name' => 'Colombia', 'iso' => 'CO', 'phonecode' => '57', 'currency' => 'COP', 'flag' => '🇨🇴', 'code' => 'colombia'],
            ['id' => 38, 'name' => 'Comoros', 'iso' => 'KM', 'phonecode' => '269', 'currency' => 'KMF', 'flag' => '🇰🇲', 'code' => 'comoros'],
            ['id' => 39, 'name' => 'Congo', 'iso' => 'CG', 'phonecode' => '242', 'currency' => 'XAF', 'flag' => '🇨🇬', 'code' => 'congo'],
            ['id' => 40, 'name' => 'Congo, Democratic Republic of the', 'iso' => 'CD', 'phonecode' => '243', 'currency' => 'CDF', 'flag' => '🇨🇩', 'code' => 'congo-democratic-republic'],
            ['id' => 41, 'name' => 'Costa Rica', 'iso' => 'CR', 'phonecode' => '506', 'currency' => 'CRC', 'flag' => '🇨🇷', 'code' => 'costa-rica'],
            ['id' => 42, 'name' => 'Croatia', 'iso' => 'HR', 'phonecode' => '385', 'currency' => 'HRK', 'flag' => '🇭🇷', 'code' => 'croatia'],
            ['id' => 43, 'name' => 'Cuba', 'iso' => 'CU', 'phonecode' => '53', 'currency' => 'CUP', 'flag' => '🇨🇺', 'code' => 'cuba'],
            ['id' => 44, 'name' => 'Cyprus', 'iso' => 'CY', 'phonecode' => '357', 'currency' => 'EUR', 'flag' => '🇨🇾', 'code' => 'cyprus'],
            ['id' => 45, 'name' => 'Czech Republic', 'iso' => 'CZ', 'phonecode' => '420', 'currency' => 'CZK', 'flag' => '🇨🇿', 'code' => 'czech-republic'],
            ['id' => 46, 'name' => 'Denmark', 'iso' => 'DK', 'phonecode' => '45', 'currency' => 'DKK', 'flag' => '🇩🇰', 'code' => 'denmark'],
            ['id' => 47, 'name' => 'Djibouti', 'iso' => 'DJ', 'phonecode' => '253', 'currency' => 'DJF', 'flag' => '🇩🇯', 'code' => 'djibouti'],
            ['id' => 48, 'name' => 'Dominica', 'iso' => 'DM', 'phonecode' => '1767', 'currency' => 'XCD', 'flag' => '🇩🇲', 'code' => 'dominica'],
            ['id' => 49, 'name' => 'Dominican Republic', 'iso' => 'DO', 'phonecode' => '1809', 'currency' => 'DOP', 'flag' => '🇩🇴', 'code' => 'dominican-republic'],
            ['id' => 50, 'name' => 'Ecuador', 'iso' => 'EC', 'phonecode' => '593', 'currency' => 'USD', 'flag' => '🇪🇨', 'code' => 'ecuador'],
            ['id' => 51, 'name' => 'Egypt', 'iso' => 'EG', 'phonecode' => '20', 'currency' => 'EGP', 'flag' => '🇪🇬', 'code' => 'egypt'],
            ['id' => 52, 'name' => 'El Salvador', 'iso' => 'SV', 'phonecode' => '503', 'currency' => 'USD', 'flag' => '🇸🇻', 'code' => 'el-salvador'],
            ['id' => 53, 'name' => 'Equatorial Guinea', 'iso' => 'GQ', 'phonecode' => '240', 'currency' => 'XAF', 'flag' => '🇬🇶', 'code' => 'equatorial-guinea'],
            ['id' => 54, 'name' => 'Eritrea', 'iso' => 'ER', 'phonecode' => '291', 'currency' => 'ERN', 'flag' => '🇪🇷', 'code' => 'eritrea'],
            ['id' => 55, 'name' => 'Estonia', 'iso' => 'EE', 'phonecode' => '372', 'currency' => 'EUR', 'flag' => '🇪🇪', 'code' => 'estonia'],
            ['id' => 56, 'name' => 'Eswatini', 'iso' => 'SZ', 'phonecode' => '268', 'currency' => 'SZL', 'flag' => '🇸🇿', 'code' => 'eswatini'],
            ['id' => 57, 'name' => 'Ethiopia', 'iso' => 'ET', 'phonecode' => '251', 'currency' => 'ETB', 'flag' => '🇪🇹', 'code' => 'ethiopia'],
            ['id' => 58, 'name' => 'Fiji', 'iso' => 'FJ', 'phonecode' => '679', 'currency' => 'FJD', 'flag' => '🇫🇯', 'code' => 'fiji'],
            ['id' => 59, 'name' => 'Finland', 'iso' => 'FI', 'phonecode' => '358', 'currency' => 'EUR', 'flag' => '🇫🇮', 'code' => 'finland'],
            ['id' => 60, 'name' => 'France', 'iso' => 'FR', 'phonecode' => '33', 'currency' => 'EUR', 'flag' => '🇫🇷', 'code' => 'france'],
            ['id' => 61, 'name' => 'Gabon', 'iso' => 'GA', 'phonecode' => '241', 'currency' => 'XAF', 'flag' => '🇬🇦', 'code' => 'gabon'],
            ['id' => 62, 'name' => 'Gambia', 'iso' => 'GM', 'phonecode' => '220', 'currency' => 'GMD', 'flag' => '🇬🇲', 'code' => 'gambia'],
            ['id' => 63, 'name' => 'Georgia', 'iso' => 'GE', 'phonecode' => '995', 'currency' => 'GEL', 'flag' => '🇬🇪', 'code' => 'georgia'],
            ['id' => 64, 'name' => 'Germany', 'iso' => 'DE', 'phonecode' => '49', 'currency' => 'EUR', 'flag' => '🇩🇪', 'code' => 'germany'],
            ['id' => 65, 'name' => 'Ghana', 'iso' => 'GH', 'phonecode' => '233', 'currency' => 'GHS', 'flag' => '🇬🇭', 'code' => 'ghana'],
            ['id' => 66, 'name' => 'Greece', 'iso' => 'GR', 'phonecode' => '30', 'currency' => 'EUR', 'flag' => '🇬🇷', 'code' => 'greece'],
            ['id' => 67, 'name' => 'Grenada', 'iso' => 'GD', 'phonecode' => '1473', 'currency' => 'XCD', 'flag' => '🇬🇩', 'code' => 'grenada'],
            ['id' => 68, 'name' => 'Guatemala', 'iso' => 'GT', 'phonecode' => '502', 'currency' => 'GTQ', 'flag' => '🇬🇹', 'code' => 'guatemala'],
            ['id' => 69, 'name' => 'Guinea', 'iso' => 'GN', 'phonecode' => '224', 'currency' => 'GNF', 'flag' => '🇬🇳', 'code' => 'guinea'],
            ['id' => 70, 'name' => 'Guinea-Bissau', 'iso' => 'GW', 'phonecode' => '245', 'currency' => 'XOF', 'flag' => '🇬🇼', 'code' => 'guinea-bissau'],
            ['id' => 71, 'name' => 'Guyana', 'iso' => 'GY', 'phonecode' => '592', 'currency' => 'GYD', 'flag' => '🇬🇾', 'code' => 'guyana'],
            ['id' => 72, 'name' => 'Haiti', 'iso' => 'HT', 'phonecode' => '509', 'currency' => 'HTG', 'flag' => '🇭🇹', 'code' => 'haiti'],
            ['id' => 73, 'name' => 'Honduras', 'iso' => 'HN', 'phonecode' => '504', 'currency' => 'HNL', 'flag' => '🇭🇳', 'code' => 'honduras'],
            ['id' => 74, 'name' => 'Hungary', 'iso' => 'HU', 'phonecode' => '36', 'currency' => 'HUF', 'flag' => '🇭🇺', 'code' => 'hungary'],
            ['id' => 75, 'name' => 'Iceland', 'iso' => 'IS', 'phonecode' => '354', 'currency' => 'ISK', 'flag' => '🇮🇸', 'code' => 'iceland'],
            ['id' => 76, 'name' => 'India', 'iso' => 'IN', 'phonecode' => '91', 'currency' => 'INR', 'flag' => '🇮🇳', 'code' => 'india'],
            ['id' => 77, 'name' => 'Indonesia', 'iso' => 'ID', 'phonecode' => '62', 'currency' => 'IDR', 'flag' => '🇮🇩', 'code' => 'indonesia'],
            ['id' => 78, 'name' => 'Iran', 'iso' => 'IR', 'phonecode' => '98', 'currency' => 'IRR', 'flag' => '🇮🇷', 'code' => 'iran'],
            ['id' => 79, 'name' => 'Iraq', 'iso' => 'IQ', 'phonecode' => '964', 'currency' => 'IQD', 'flag' => '🇮🇶', 'code' => 'iraq'],
            ['id' => 80, 'name' => 'Ireland', 'iso' => 'IE', 'phonecode' => '353', 'currency' => 'EUR', 'flag' => '🇮🇪', 'code' => 'ireland'],
            ['id' => 81, 'name' => 'Israel', 'iso' => 'IL', 'phonecode' => '972', 'currency' => 'ILS', 'flag' => '🇮🇱', 'code' => 'israel'],
            ['id' => 82, 'name' => 'Italy', 'iso' => 'IT', 'phonecode' => '39', 'currency' => 'EUR', 'flag' => '🇮🇹', 'code' => 'italy'],
            ['id' => 83, 'name' => 'Jamaica', 'iso' => 'JM', 'phonecode' => '1876', 'currency' => 'JMD', 'flag' => '🇯🇲', 'code' => 'jamaica'],
            ['id' => 84, 'name' => 'Japan', 'iso' => 'JP', 'phonecode' => '81', 'currency' => 'JPY', 'flag' => '🇯🇵', 'code' => 'japan'],
            ['id' => 85, 'name' => 'Jordan', 'iso' => 'JO', 'phonecode' => '962', 'currency' => 'JOD', 'flag' => '🇯🇴', 'code' => 'jordan'],
            ['id' => 86, 'name' => 'Kazakhstan', 'iso' => 'KZ', 'phonecode' => '7', 'currency' => 'KZT', 'flag' => '🇰🇿', 'code' => 'kazakhstan'],
            ['id' => 87, 'name' => 'Kenya', 'iso' => 'KE', 'phonecode' => '254', 'currency' => 'KES', 'flag' => '🇰🇪', 'code' => 'kenya'],
            ['id' => 88, 'name' => 'Kiribati', 'iso' => 'KI', 'phonecode' => '686', 'currency' => 'AUD', 'flag' => '🇰🇮', 'code' => 'kiribati'],
            ['id' => 89, 'name' => 'Korea, Democratic People\'s Republic of', 'iso' => 'KP', 'phonecode' => '850', 'currency' => 'KPW', 'flag' => '🇰🇵', 'code' => 'korea-democratic-peoples-republic-of'],
            ['id' => 90, 'name' => 'Korea, Republic of', 'iso' => 'KR', 'phonecode' => '82', 'currency' => 'KRW', 'flag' => '🇰🇷', 'code' => 'korea-republic-of'],
            ['id' => 91, 'name' => 'Kuwait', 'iso' => 'KW', 'phonecode' => '965', 'currency' => 'KWD', 'flag' => '🇰🇼', 'code' => 'kuwait'],
            ['id' => 92, 'name' => 'Kyrgyzstan', 'iso' => 'KG', 'phonecode' => '996', 'currency' => 'KGS', 'flag' => '🇰🇬', 'code' => 'kyrgyzstan'],
            ['id' => 93, 'name' => 'Lao People\'s Democratic Republic', 'iso' => 'LA', 'phonecode' => '856', 'currency' => 'LAK', 'flag' => '🇱🇦', 'code' => 'lao-peoples-democratic-republic'],
            ['id' => 94, 'name' => 'Latvia', 'iso' => 'LV', 'phonecode' => '371', 'currency' => 'EUR', 'flag' => '🇱🇻', 'code' => 'latvia'],
            ['id' => 95, 'name' => 'Lebanon', 'iso' => 'LB', 'phonecode' => '961', 'currency' => 'LBP', 'flag' => '🇱🇧', 'code' => 'lebanon'],
            ['id' => 96, 'name' => 'Lesotho', 'iso' => 'LS', 'phonecode' => '266', 'currency' => 'LSL', 'flag' => '🇱🇸', 'code' => 'lesotho'],
            ['id' => 97, 'name' => 'Liberia', 'iso' => 'LR', 'phonecode' => '231', 'currency' => 'LRD', 'flag' => '🇱🇷', 'code' => 'liberia'],
            ['id' => 98, 'name' => 'Libya', 'iso' => 'LY', 'phonecode' => '218', 'currency' => 'LYD', 'flag' => '🇱🇾', 'code' => 'libya'],
            ['id' => 99, 'name' => 'Liechtenstein', 'iso' => 'LI', 'phonecode' => '423', 'currency' => 'CHF', 'flag' => '🇱🇮', 'code' => 'liechtenstein'],
            ['id' => 100, 'name' => 'Lithuania', 'iso' => 'LT', 'phonecode' => '370', 'currency' => 'EUR', 'flag' => '🇱🇹', 'code' => 'lithuania'],
            ['id' => 101, 'name' => 'Luxembourg', 'iso' => 'LU', 'phonecode' => '352', 'currency' => 'EUR', 'flag' => '🇱🇺', 'code' => 'luxembourg'],
            ['id' => 102, 'name' => 'Madagascar', 'iso' => 'MG', 'phonecode' => '261', 'currency' => 'MGA', 'flag' => '🇲🇬', 'code' => 'madagascar'],
            ['id' => 103, 'name' => 'Malawi', 'iso' => 'MW', 'phonecode' => '265', 'currency' => 'MWK', 'flag' => '🇲🇼', 'code' => 'malawi'],
            ['id' => 104, 'name' => 'Malaysia', 'iso' => 'MY', 'phonecode' => '60', 'currency' => 'MYR', 'flag' => '🇲🇾', 'code' => 'malaysia'],
            ['id' => 105, 'name' => 'Maldives', 'iso' => 'MV', 'phonecode' => '960', 'currency' => 'MVR', 'flag' => '🇲🇻', 'code' => 'maldives'],
            ['id' => 106, 'name' => 'Mali', 'iso' => 'ML', 'phonecode' => '223', 'currency' => 'XOF', 'flag' => '🇲🇱', 'code' => 'mali'],
            ['id' => 107, 'name' => 'Malta', 'iso' => 'MT', 'phonecode' => '356', 'currency' => 'EUR', 'flag' => '🇲🇹', 'code' => 'malta'],
            ['id' => 108, 'name' => 'Marshall Islands', 'iso' => 'MH', 'phonecode' => '692', 'currency' => 'USD', 'flag' => '🇲🇭', 'code' => 'marshall-islands'],
            ['id' => 109, 'name' => 'Mauritania', 'iso' => 'MR', 'phonecode' => '222', 'currency' => 'MRU', 'flag' => '🇲🇷', 'code' => 'mauritania'],
            ['id' => 110, 'name' => 'Mauritius', 'iso' => 'MU', 'phonecode' => '230', 'currency' => 'MUR', 'flag' => '🇲🇺', 'code' => 'mauritius'],
            ['id' => 111, 'name' => 'Mexico', 'iso' => 'MX', 'phonecode' => '52', 'currency' => 'MXN', 'flag' => '🇲🇽', 'code' => 'mexico'],
            ['id' => 112, 'name' => 'Micronesia (Federated States of)', 'iso' => 'FM', 'phonecode' => '691', 'currency' => 'USD', 'flag' => '🇫🇲', 'code' => 'micronesia-federated-states-of'],
            ['id' => 113, 'name' => 'Moldova (Republic of)', 'iso' => 'MD', 'phonecode' => '373', 'currency' => 'MDL', 'flag' => '🇲🇩', 'code' => 'moldova-republic-of'],
            ['id' => 114, 'name' => 'Monaco', 'iso' => 'MC', 'phonecode' => '377', 'currency' => 'EUR', 'flag' => '🇲🇨', 'code' => 'monaco'],
            ['id' => 115, 'name' => 'Mongolia', 'iso' => 'MN', 'phonecode' => '976', 'currency' => 'MNT', 'flag' => '🇲🇳', 'code' => 'mongolia'],
            ['id' => 116, 'name' => 'Montenegro', 'iso' => 'ME', 'phonecode' => '382', 'currency' => 'EUR', 'flag' => '🇲🇪', 'code' => 'montenegro'],
            ['id' => 117, 'name' => 'Morocco', 'iso' => 'MA', 'phonecode' => '212', 'currency' => 'MAD', 'flag' => '🇲🇦', 'code' => 'morocco'],
            ['id' => 118, 'name' => 'Mozambique', 'iso' => 'MZ', 'phonecode' => '258', 'currency' => 'MZN', 'flag' => '🇲🇿', 'code' => 'mozambique'],
            ['id' => 119, 'name' => 'Myanmar', 'iso' => 'MM', 'phonecode' => '95', 'currency' => 'MMK', 'flag' => '🇲🇲', 'code' => 'myanmar'],
            ['id' => 120, 'name' => 'Namibia', 'iso' => 'NA', 'phonecode' => '264', 'currency' => 'NAD', 'flag' => '🇳🇦', 'code' => 'namibia'],
            ['id' => 121, 'name' => 'Nauru', 'iso' => 'NR', 'phonecode' => '674', 'currency' => 'AUD', 'flag' => '🇳🇷', 'code' => 'nauru'],
            ['id' => 122, 'name' => 'Nepal', 'iso' => 'NP', 'phonecode' => '977', 'currency' => 'NPR', 'flag' => '🇳🇵', 'code' => 'nepal'],
            ['id' => 123, 'name' => 'Netherlands', 'iso' => 'NL', 'phonecode' => '31', 'currency' => 'EUR', 'flag' => '🇳🇱', 'code' => 'netherlands'],
            ['id' => 124, 'name' => 'New Zealand', 'iso' => 'NZ', 'phonecode' => '64', 'currency' => 'NZD', 'flag' => '🇳🇿', 'code' => 'new-zealand'],
            ['id' => 125, 'name' => 'Nicaragua', 'iso' => 'NI', 'phonecode' => '505', 'currency' => 'NIO', 'flag' => '🇳🇮', 'code' => 'nicaragua'],
            ['id' => 126, 'name' => 'Niger', 'iso' => 'NE', 'phonecode' => '227', 'currency' => 'XOF', 'flag' => '🇳🇪', 'code' => 'niger'],
            ['id' => 127, 'name' => 'Nigeria', 'iso' => 'NG', 'phonecode' => '234', 'currency' => 'NGN', 'flag' => '🇳🇬', 'code' => 'nigeria'],
            ['id' => 128, 'name' => 'North Macedonia', 'iso' => 'MK', 'phonecode' => '389', 'currency' => 'MKD', 'flag' => '🇲🇰', 'code' => 'north-macedonia'],
            ['id' => 129, 'name' => 'Norway', 'iso' => 'NO', 'phonecode' => '47', 'currency' => 'NOK', 'flag' => '🇳🇴', 'code' => 'norway'],
            ['id' => 130, 'name' => 'Oman', 'iso' => 'OM', 'phonecode' => '968', 'currency' => 'OMR', 'flag' => '🇴🇲', 'code' => 'oman'],
            ['id' => 131, 'name' => 'Pakistan', 'iso' => 'PK', 'phonecode' => '92', 'currency' => 'PKR', 'flag' => '🇵🇰', 'code' => 'pakistan'],
            ['id' => 132, 'name' => 'Palau', 'iso' => 'PW', 'phonecode' => '680', 'currency' => 'USD', 'flag' => '🇵🇼', 'code' => 'palau'],
            ['id' => 133, 'name' => 'Palestine, State of', 'iso' => 'PS', 'phonecode' => '970', 'currency' => 'ILS', 'flag' => '🇵🇸', 'code' => 'palestine-state-of'],
            ['id' => 134, 'name' => 'Panama', 'iso' => 'PA', 'phonecode' => '507', 'currency' => 'PAB', 'flag' => '🇵🇦', 'code' => 'panama'],
            ['id' => 135, 'name' => 'Papua New Guinea', 'iso' => 'PG', 'phonecode' => '675', 'currency' => 'PGK', 'flag' => '🇵🇬', 'code' => 'papua-new-guinea'],
            ['id' => 136, 'name' => 'Paraguay', 'iso' => 'PY', 'phonecode' => '595', 'currency' => 'PYG', 'flag' => '🇵🇾', 'code' => 'paraguay'],
            ['id' => 137, 'name' => 'Peru', 'iso' => 'PE', 'phonecode' => '51', 'currency' => 'PEN', 'flag' => '🇵🇪', 'code' => 'peru'],
            ['id' => 138, 'name' => 'Philippines', 'iso' => 'PH', 'phonecode' => '63', 'currency' => 'PHP', 'flag' => '🇵🇭', 'code' => 'philippines'],
            ['id' => 139, 'name' => 'Poland', 'iso' => 'PL', 'phonecode' => '48', 'currency' => 'PLN', 'flag' => '🇵🇱', 'code' => 'poland'],
            ['id' => 140, 'name' => 'Portugal', 'iso' => 'PT', 'phonecode' => '351', 'currency' => 'EUR', 'flag' => '🇵🇹', 'code' => 'portugal'],
            ['id' => 141, 'name' => 'Qatar', 'iso' => 'QA', 'phonecode' => '974', 'currency' => 'QAR', 'flag' => '🇶🇦', 'code' => 'qatar'],
            ['id' => 142, 'name' => 'Romania', 'iso' => 'RO', 'phonecode' => '40', 'currency' => 'RON', 'flag' => '🇷🇴', 'code' => 'romania'],
            ['id' => 143, 'name' => 'Russian Federation', 'iso' => 'RU', 'phonecode' => '7', 'currency' => 'RUB', 'flag' => '🇷🇺', 'code' => 'russian-federation'],
            ['id' => 144, 'name' => 'Rwanda', 'iso' => 'RW', 'phonecode' => '250', 'currency' => 'RWF', 'flag' => '🇷🇼', 'code' => 'rwanda'],
            ['id' => 145, 'name' => 'Saint Kitts and Nevis', 'iso' => 'KN', 'phonecode' => '1869', 'currency' => 'XCD', 'flag' => '🇰🇳', 'code' => 'saint-kitts-and-nevis'],
            ['id' => 146, 'name' => 'Saint Lucia', 'iso' => 'LC', 'phonecode' => '1758', 'currency' => 'XCD', 'flag' => '🇱🇨', 'code' => 'saint-lucia'],
            ['id' => 147, 'name' => 'Saint Vincent and the Grenadines', 'iso' => 'VC', 'phonecode' => '1784', 'currency' => 'XCD', 'flag' => '🇻🇨', 'code' => 'saint-vincent-and-the-grenadines'],
            ['id' => 148, 'name' => 'Samoa', 'iso' => 'WS', 'phonecode' => '685', 'currency' => 'WST', 'flag' => '🇼🇸', 'code' => 'samoa'],
            ['id' => 149, 'name' => 'San Marino', 'iso' => 'SM', 'phonecode' => '378', 'currency' => 'EUR', 'flag' => '🇸🇲', 'code' => 'san-marino'],
            ['id' => 150, 'name' => 'Sao Tome and Principe', 'iso' => 'ST', 'phonecode' => '239', 'currency' => 'STN', 'flag' => '🇸🇹', 'code' => 'sao-tome-and-principe'],
            ['id' => 151, 'name' => 'Saudi Arabia', 'iso' => 'SA', 'phonecode' => '966', 'currency' => 'SAR', 'flag' => '🇸🇦', 'code' => 'saudi-arabia'],
            ['id' => 152, 'name' => 'Senegal', 'iso' => 'SN', 'phonecode' => '221', 'currency' => 'XOF', 'flag' => '🇸🇳', 'code' => 'senegal'],
            ['id' => 153, 'name' => 'Serbia', 'iso' => 'RS', 'phonecode' => '381', 'currency' => 'RSD', 'flag' => '🇷🇸', 'code' => 'serbia'],
            ['id' => 154, 'name' => 'Seychelles', 'iso' => 'SC', 'phonecode' => '248', 'currency' => 'SCR', 'flag' => '🇸🇨', 'code' => 'seychelles'],
            ['id' => 155, 'name' => 'Sierra Leone', 'iso' => 'SL', 'phonecode' => '232', 'currency' => 'SLL', 'flag' => '🇸🇱', 'code' => 'sierra-leone'],
            ['id' => 156, 'name' => 'Singapore', 'iso' => 'SG', 'phonecode' => '65', 'currency' => 'SGD', 'flag' => '🇸🇬', 'code' => 'singapore'],
            ['id' => 157, 'name' => 'Slovakia', 'iso' => 'SK', 'phonecode' => '421', 'currency' => 'EUR', 'flag' => '🇸🇰', 'code' => 'slovakia'],
            ['id' => 158, 'name' => 'Slovenia', 'iso' => 'SI', 'phonecode' => '386', 'currency' => 'EUR', 'flag' => '🇸🇮', 'code' => 'slovenia'],
            ['id' => 159, 'name' => 'Solomon Islands', 'iso' => 'SB', 'phonecode' => '677', 'currency' => 'SBD', 'flag' => '🇸🇧', 'code' => 'solomon-islands'],
            ['id' => 160, 'name' => 'Somalia', 'iso' => 'SO', 'phonecode' => '252', 'currency' => 'SOS', 'flag' => '🇸🇴', 'code' => 'somalia'],
            ['id' => 161, 'name' => 'South Africa', 'iso' => 'ZA', 'phonecode' => '27', 'currency' => 'ZAR', 'flag' => '🇿🇦', 'code' => 'south-africa'],
            ['id' => 162, 'name' => 'South Sudan', 'iso' => 'SS', 'phonecode' => '211', 'currency' => 'SSP', 'flag' => '🇸🇸', 'code' => 'south-sudan'],
            ['id' => 163, 'name' => 'Spain', 'iso' => 'ES', 'phonecode' => '34', 'currency' => 'EUR', 'flag' => '🇪🇸', 'code' => 'spain'],
            ['id' => 164, 'name' => 'Sri Lanka', 'iso' => 'LK', 'phonecode' => '94', 'currency' => 'LKR', 'flag' => '🇱🇰', 'code' => 'sri-lanka'],
            ['id' => 165, 'name' => 'Sudan', 'iso' => 'SD', 'phonecode' => '249', 'currency' => 'SDG', 'flag' => '🇸🇩', 'code' => 'sudan'],
            ['id' => 166, 'name' => 'Suriname', 'iso' => 'SR', 'phonecode' => '597', 'currency' => 'SRD', 'flag' => '🇸🇷', 'code' => 'suriname'],
            ['id' => 167, 'name' => 'Sweden', 'iso' => 'SE', 'phonecode' => '46', 'currency' => 'SEK', 'flag' => '🇸🇪', 'code' => 'sweden'],
            ['id' => 168, 'name' => 'Switzerland', 'iso' => 'CH', 'phonecode' => '41', 'currency' => 'CHF', 'flag' => '🇨🇭', 'code' => 'switzerland'],
            ['id' => 169, 'name' => 'Syrian Arab Republic', 'iso' => 'SY', 'phonecode' => '963', 'currency' => 'SYP', 'flag' => '🇸🇾', 'code' => 'syrian-arab-republic'],
            ['id' => 170, 'name' => 'Tajikistan', 'iso' => 'TJ', 'phonecode' => '992', 'currency' => 'TJS', 'flag' => '🇹🇯', 'code' => 'tajikistan'],
            ['id' => 171, 'name' => 'Tanzania, United Republic of', 'iso' => 'TZ', 'phonecode' => '255', 'currency' => 'TZS', 'flag' => '🇹🇿', 'code' => 'tanzania-united-republic-of'],
            ['id' => 172, 'name' => 'Thailand', 'iso' => 'TH', 'phonecode' => '66', 'currency' => 'THB', 'flag' => '🇹🇭', 'code' => 'thailand'],
            ['id' => 173, 'name' => 'Timor-Leste', 'iso' => 'TL', 'phonecode' => '670', 'currency' => 'USD', 'flag' => '🇹🇱', 'code' => 'timor-leste'],
            ['id' => 174, 'name' => 'Togo', 'iso' => 'TG', 'phonecode' => '228', 'currency' => 'XOF', 'flag' => '🇹🇬', 'code' => 'togo'],
            ['id' => 175, 'name' => 'Tonga', 'iso' => 'TO', 'phonecode' => '676', 'currency' => 'TOP', 'flag' => '🇹🇴', 'code' => 'tonga'],
            ['id' => 176, 'name' => 'Trinidad and Tobago', 'iso' => 'TT', 'phonecode' => '1868', 'currency' => 'TTD', 'flag' => '🇹🇹', 'code' => 'trinidad-and-tobago'],
            ['id' => 177, 'name' => 'Tunisia', 'iso' => 'TN', 'phonecode' => '216', 'currency' => 'TND', 'flag' => '🇹🇳', 'code' => 'tunisia'],
            ['id' => 178, 'name' => 'Turkey', 'iso' => 'TR', 'phonecode' => '90', 'currency' => 'TRY', 'flag' => '🇹🇷', 'code' => 'turkey'],
            ['id' => 179, 'name' => 'Turkmenistan', 'iso' => 'TM', 'phonecode' => '993', 'currency' => 'TMT', 'flag' => '🇹🇲', 'code' => 'turkmenistan'],
            ['id' => 180, 'name' => 'Tuvalu', 'iso' => 'TV', 'phonecode' => '688', 'currency' => 'AUD', 'flag' => '🇹🇻', 'code' => 'tuvalu'],
            ['id' => 181, 'name' => 'Uganda', 'iso' => 'UG', 'phonecode' => '256', 'currency' => 'UGX', 'flag' => '🇺🇬', 'code' => 'uganda'],
            ['id' => 182, 'name' => 'Ukraine', 'iso' => 'UA', 'phonecode' => '380', 'currency' => 'UAH', 'flag' => '🇺🇦', 'code' => 'ukraine'],
            ['id' => 183, 'name' => 'United Arab Emirates', 'iso' => 'AE', 'phonecode' => '971', 'currency' => 'AED', 'flag' => '🇦🇪', 'code' => 'united-arab-emirates'],
            ['id' => 184, 'name' => 'United Kingdom of Great Britain and Northern Ireland', 'iso' => 'GB', 'phonecode' => '44', 'currency' => 'GBP', 'flag' => '🇬🇧', 'code' => 'united-kingdom-of-great-britain-and-northern-ireland'],
            ['id' => 185, 'name' => 'United States of America', 'iso' => 'US', 'phonecode' => '1', 'currency' => 'USD', 'flag' => '🇺🇸', 'code' => 'united-states-of-america'],
            ['id' => 186, 'name' => 'Uruguay', 'iso' => 'UY', 'phonecode' => '598', 'currency' => 'UYU', 'flag' => '🇺🇾', 'code' => 'uruguay'],
            ['id' => 187, 'name' => 'Uzbekistan', 'iso' => 'UZ', 'phonecode' => '998', 'currency' => 'UZS', 'flag' => '🇺🇿', 'code' => 'uzbekistan'],
            ['id' => 188, 'name' => 'Vanuatu', 'iso' => 'VU', 'phonecode' => '678', 'currency' => 'VUV', 'flag' => '🇻🇺', 'code' => 'vanuatu'],
            ['id' => 189, 'name' => 'Venezuela (Bolivarian Republic of)', 'iso' => 'VE', 'phonecode' => '58', 'currency' => 'VES', 'flag' => '🇻🇪', 'code' => 'venezuela-bolivarian-republic-of'],
            ['id' => 190, 'name' => 'Viet Nam', 'iso' => 'VN', 'phonecode' => '84', 'currency' => 'VND', 'flag' => '🇻🇳', 'code' => 'viet-nam'],
            ['id' => 191, 'name' => 'Yemen', 'iso' => 'YE', 'phonecode' => '967', 'currency' => 'YER', 'flag' => '🇾🇪', 'code' => 'yemen'],
            ['id' => 192, 'name' => 'Zambia', 'iso' => 'ZM', 'phonecode' => '260', 'currency' => 'ZMW', 'flag' => '🇿🇲', 'code' => 'zambia'],
            ['id' => 193, 'name' => 'Zimbabwe', 'iso' => 'ZW', 'phonecode' => '263', 'currency' => 'ZWL', 'flag' => '🇿🇼', 'code' => 'zimbabwe']
        ];

        // Insert countries in chunks to avoid memory issues
        foreach (array_chunk($countries, 50) as $chunk) {
            Country::insert($chunk);
        }
    }
}