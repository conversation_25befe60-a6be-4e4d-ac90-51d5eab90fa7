<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('owner_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('owner_id');
            $table->foreignId('apartment_id')->nullable();
            $table->foreignId('booking_id')->nullable();
            $table->foreignId('owner_payout_id')->nullable();
            $table->string('amount');
            $table->string('type');
            $table->text('details');
            $table->string('reference')->unique();
            $table->string('code')->unique();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('owner_transactions');
    }
};
