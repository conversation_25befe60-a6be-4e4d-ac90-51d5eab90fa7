<?php

namespace App\Utilities;

use Illuminate\Contracts\Filesystem\Filesystem;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\File\File;

class MediaUploader
{
    /**
     * @var Filesystem
     */
    private Filesystem $storage;

    /**
     * MediaUploader constructor.
     */
    public function __construct()
    {
        $this->storage = Storage::disk('local');
    }

    /**
     * @param string $folder
     * @param UploadedFile $request
     * @return string|null
     */
    public function upload(string $folder, UploadedFile $request): ?string
    {
        $file = $this->storage->put($folder, $request);
        if(is_null($file)){
            $this->storage = $this->switch_disk('local');

            $file = $this->storage->put($folder, $request);
            $url = $this->storage->url($file);
            $this->storage->setVisibility($file, 'public');
        }
        else{
            $url = $this->storage->url($file);
            $this->storage->setVisibility($file, 'public');
        }

        return $url ?? null;
    }

    /**
     * @param $file
     * @return bool
     */
    public function exists($file): bool
    {
        return $this->storage->exists(self::prepare_file($file));
    }

    /**
     * @param $file
     * @return int
     */
    public function size($file): int
    {
        return $this->storage->size(self::prepare_file($file));
    }

    /**
     * @param $file
     * @return bool
     */
    public function delete($file): bool
    {
        return app()->environment() == 'local' ? $this->storage->delete($file) : $this->storage->delete(self::prepare_file($file));
    }

    /**
     * @param $file
     * @return string|string[]
     */
    public static function prepare_file($file): array|string
    {
        return str_replace('https://ngworkers.s3.eu-central-1.wasabisys.com/', '', $file);
    }

    /**
     * @param $disk
     * @return Filesystem
     */
    public function switch_disk($disk): Filesystem
    {
        return Storage::disk($disk);
    }

    /**
     * @param $base64
     * @return UploadedFile
     */
    public function convert_base64_to_uploaded_file($base64)
    {
        $fileData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $base64));

        $tmpFilePath = sys_get_temp_dir() . '/' . Str::uuid()->toString();
        file_put_contents($tmpFilePath, $fileData);

        $tmpFile = new File($tmpFilePath);

        return new UploadedFile(
            $tmpFile->getPathname(),
            $tmpFile->getFilename(),
            $tmpFile->getMimeType(),
            0,
            true // Mark it as test, since the file isn't from real HTTP POST.
        );
    }

    /**
     * @param $base64
     * @return bool|JsonResponse
     */
    public function check_uploaded_file($base64): JsonResponse|bool
    {
        $allowedMimeTypes = ['image/jpeg', 'image/png'];

        if (preg_match('#^data:(image/\w+);base64,#i', $base64, $matches)) {
            $mimeType = $matches[1];

            if (!in_array($mimeType, $allowedMimeTypes)) {
                return response()->json([RequestStatus::ERROR => 'Invalid image type. Only .jpg, .jpeg, and .png are allowed.'], Response::HTTP_BAD_REQUEST);
            }
        }
        else {
            return response()->json([RequestStatus::ERROR => 'Invalid base64 string.'], Response::HTTP_BAD_REQUEST);
        }

        return true;
    }
}
