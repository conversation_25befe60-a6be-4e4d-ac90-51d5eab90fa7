import { Button } from "@/Components/ui/button";
import { Input } from "@/Components/ui/input";
import { cn } from "@/lib/utils";
import { usePage, router } from "@inertiajs/react";
import { Search, X, Ban, Table, Eye, ChevronLeft } from "lucide-react";
import Countries from "./countries";
import ViewCountry from "./view-country";
import { CountriesData, Country } from "@/types/models/country";
import { useState, useCallback } from "react";
import { ConfirmationDialog } from "@/Components/ui/confirmation-dialog";
import { toast } from "sonner";

type Props = {
    className?: string;
};

type ViewMode = "table" | "detail";

export default function Locations({ className }: Props) {
    const { countries, filters } = usePage().props;
    const [searchValue, setSearchValue] = useState(
        (filters as any)?.search || ""
    );
    const [selectedCountries, setSelectedCountries] = useState<Country[]>([]);
    const [showConfirmDialog, setShowConfirmDialog] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [singleCountryToSuspend, setSingleCountryToSuspend] =
        useState<Country | null>(null);
    const [showSingleConfirmDialog, setShowSingleConfirmDialog] =
        useState(false);

    // View switching state
    const [viewMode, setViewMode] = useState<ViewMode>("table");
    const [selectedCountry, setSelectedCountry] = useState<Country | null>(
        null
    );
    const [isViewSwitching, setIsViewSwitching] = useState(false);

    // Debounce search to avoid too many API calls
    const debouncedSearch = useCallback((value: string) => {
        const timeoutId = setTimeout(() => {
            router.get(
                route("settings"),
                {
                    search: value,
                    page: 1, // Reset to first page when searching
                },
                {
                    preserveState: true,
                    preserveScroll: true,
                }
            );
        }, 500);

        return () => clearTimeout(timeoutId);
    }, []);

    const handleSearchChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            const value = e.target.value;
            setSearchValue(value);
            debouncedSearch(value);
        },
        [debouncedSearch]
    );

    // Clear search
    const handleClearSearch = useCallback(() => {
        setSearchValue("");
        router.get(
            route("settings"),
            {
                search: "",
                page: 1,
            },
            {
                preserveState: true,
                preserveScroll: true,
            }
        );
    }, []);

    // Handle selection change from DataTable
    const handleSelectionChange = useCallback((selected: Country[]) => {
        setSelectedCountries(selected);
    }, []);

    // Handle bulk suspend
    const handleBulkSuspend = useCallback(() => {
        if (selectedCountries.length === 0) return;
        setShowConfirmDialog(true);
    }, [selectedCountries]);

    // Combined function to handle both single and bulk suspend/restore operations
    const confirmSuspendOperation = useCallback((countries: Country[]) => {
        if (countries.length === 0) return;

        setIsLoading(true);

        router.post(
            route("settings.countries.bulk-suspend"),
            {
                country_ids: countries.map((country) => country.id),
            },
            {
                preserveState: true,
                preserveScroll: true,
                onSuccess: (page) => {
                    const response = page.props.flash as any;
                    if (response?.success) {
                        toast.success(
                            response.message ||
                                `${
                                    countries.length === 1
                                        ? "Country"
                                        : "Countries"
                                } processed successfully`
                        );
                    }

                    // Close appropriate dialog and reset state
                    setShowConfirmDialog(false);
                    setShowSingleConfirmDialog(false);
                    setSelectedCountries([]);
                    setSingleCountryToSuspend(null);
                },
                onError: (errors) => {
                    console.error("Suspend operation errors:", errors);
                    const errorMessage =
                        (Object.values(errors)[0] as string) ||
                        `Failed to process ${
                            countries.length === 1 ? "country" : "countries"
                        }`;
                    toast.error(errorMessage);
                },
                onFinish: () => {
                    setIsLoading(false);
                },
            }
        );
    }, []);

    const confirmBulkSuspend = useCallback(() => {
        confirmSuspendOperation(selectedCountries);
    }, [selectedCountries, confirmSuspendOperation]);

    // Handle single country suspend from dropdown
    const handleSingleSuspend = useCallback((country: Country) => {
        setSingleCountryToSuspend(country);
        setShowSingleConfirmDialog(true);
    }, []);

    const confirmSingleSuspend = useCallback(() => {
        if (singleCountryToSuspend) {
            confirmSuspendOperation([singleCountryToSuspend]);
        }
    }, [singleCountryToSuspend, confirmSuspendOperation]);

    // View switching handlers
    const handleViewCountry = useCallback((country: Country) => {
        setIsViewSwitching(true);
        setTimeout(() => {
            setSelectedCountry(country);
            setViewMode("detail");
            setIsViewSwitching(false);
        }, 150);
    }, []);

    const handleBackToTable = useCallback(() => {
        setIsViewSwitching(true);
        setTimeout(() => {
            setViewMode("table");
            setSelectedCountry(null);
            setIsViewSwitching(false);
        }, 150);
    }, []);

    return (
        <div className={cn(className, "flex flex-col gap-6")}>
            {/* Header with view switching */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    {viewMode !== "detail" && !selectedCountry && (
                        <>
                            <h5 className="font-bold">Location Management</h5>
                        </>
                    )}
                </div>
            </div>

            {/* Conditional Content Rendering */}
            <div className="transition-all duration-300 ease-in-out">
                {viewMode === "table" ? (
                    <div className="animate-in fade-in-0 slide-in-from-left-2 duration-300">
                        {/* Table View Content */}
                        <div className="flex items-center justify-between mb-6">
                            <div className="flex items-center gap-4 w-full max-w-lg">
                                <div className="relative w-full">
                                    <Input
                                        className="rounded pr-10"
                                        placeholder="Search for countries"
                                        value={searchValue}
                                        onChange={handleSearchChange}
                                    />
                                    {searchValue ? (
                                        <X
                                            className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400 hover:text-gray-600 cursor-pointer"
                                            onClick={handleClearSearch}
                                        />
                                    ) : (
                                        <Search className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                                    )}
                                </div>
                            </div>
                            <div className="flex gap-4">
                                <Button
                                    onClick={handleBulkSuspend}
                                    disabled={
                                        selectedCountries.length === 0 ||
                                        isLoading
                                    }
                                    className={cn(
                                        "min-w-[154px] bg-[#F0860C] hover:bg-[#F0860C] disabled:bg-gray-300 disabled:cursor-not-allowed",
                                        selectedCountries.length === 0 &&
                                            "opacity-50"
                                    )}
                                >
                                    <Ban className="" />
                                    {selectedCountries.length > 0
                                        ? `Suspend ${
                                              selectedCountries.length
                                          } ${
                                              selectedCountries.length === 1
                                                  ? "Country"
                                                  : "Countries"
                                          }`
                                        : "Bulk Suspend"}
                                </Button>
                            </div>
                        </div>

                        {/* Search Results Indicator */}
                        {searchValue && (
                            <div className="flex items-center justify-between bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
                                <div className="flex items-center gap-2">
                                    <Search className="h-4 w-4 text-green-600" />
                                    <span className="text-sm text-green-800">
                                        Search results for "{searchValue}" -{" "}
                                        {(countries as CountriesData).total}{" "}
                                        countries found
                                    </span>
                                </div>
                                <button
                                    onClick={handleClearSearch}
                                    className="text-green-600 hover:text-green-800 text-sm font-medium"
                                >
                                    Clear search
                                </button>
                            </div>
                        )}

                        <Countries
                            countries={countries as CountriesData}
                            onSelectionChange={handleSelectionChange}
                            onSingleSuspend={handleSingleSuspend}
                            onViewCountry={handleViewCountry}
                        />
                    </div>
                ) : (
                    <div className="animate-in fade-in-0 slide-in-from-right-2 duration-300">
                        {/* Detail View Content */}
                        <div className="flex items-center gap-4 mb-4">
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={handleBackToTable}
                                className="text-gray-600 hover:text-gray-800"
                            >
                                <ChevronLeft className="h-4 w-4 mr-1" />
                                Back to Countries
                            </Button>
                        </div>

                        {selectedCountry && (
                            <ViewCountry
                                country={selectedCountry}
                                states={[]}
                                hideBackButton={true}
                            />
                        )}
                    </div>
                )}
            </div>

            {/* Confirmation Dialog */}
            <ConfirmationDialog
                open={showConfirmDialog}
                onOpenChange={setShowConfirmDialog}
                title="Suspend Countries"
                description={`Are you sure you want to suspend ${
                    selectedCountries.length
                } ${
                    selectedCountries.length === 1 ? "country" : "countries"
                }? This action will make ${
                    selectedCountries.length === 1 ? "it" : "them"
                } unavailable for selection.`}
                confirmText="Suspend"
                cancelText="Cancel"
                onConfirm={confirmBulkSuspend}
                variant="warning"
                loading={isLoading}
            />

            {/* Single Country Confirmation Dialog */}
            <ConfirmationDialog
                open={showSingleConfirmDialog}
                onOpenChange={setShowSingleConfirmDialog}
                title={
                    singleCountryToSuspend?.deleted_at
                        ? "Restore Country"
                        : "Suspend Country"
                }
                description={
                    singleCountryToSuspend?.deleted_at
                        ? `Are you sure you want to restore "${singleCountryToSuspend?.name}"? This will make it available for selection again.`
                        : `Are you sure you want to suspend "${singleCountryToSuspend?.name}"? This action will make it unavailable for selection.`
                }
                confirmText={
                    singleCountryToSuspend?.deleted_at ? "Restore" : "Suspend"
                }
                cancelText="Cancel"
                onConfirm={confirmSingleSuspend}
                variant="warning"
                loading={isLoading}
            />
        </div>
    );
}
