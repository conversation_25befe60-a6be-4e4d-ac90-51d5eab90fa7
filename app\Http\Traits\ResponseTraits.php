<?php

namespace App\Http\Traits;

use App\Utilities\RequestStatus;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Http\Resources\Json\JsonResource;

trait ResponseTraits
{
    /**
     * Return a successful response with data
     *
     * @param mixed $data
     * @param int $statusCode
     * @return JsonResponse
     */
    protected function success($data = null, int $statusCode = Response::HTTP_OK): JsonResponse
    {
        $response = [RequestStatus::SUCCESS => true];

        if ($data instanceof JsonResource) {
            $response[RequestStatus::DATA] = $data;
        }
        elseif (is_array($data)) {
            if (array_keys($data) !== range(0, count($data) - 1)) {
                $response = array_merge($response, $data);
            } else {
                $response[RequestStatus::DATA] = $data;
            }
        }
        elseif ($data !== null) {
            $response[RequestStatus::DATA] = $data;
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Return an error response
     *
     * @param string $message
     * @param string $errorType
     * @param int $statusCode
     * @param array $additionalData
     * @return JsonResponse
     */
    protected function error(
        string $message,
        string $errorType = RequestStatus::ERROR,
        int $statusCode = Response::HTTP_BAD_REQUEST,
        array $additionalData = []
    ): JsonResponse {
        $response = [
            RequestStatus::SUCCESS => false,
            RequestStatus::MESSAGE => $message,
            'error_type' => $errorType,
        ];

        if (!empty($additionalData)) {
            $response = array_merge($response, $additionalData);
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Return a validation error response
     *
     * @param array $errors
     * @param string $message
     * @return JsonResponse
     */
    protected function validationError(array $errors, string $message = 'Validation failed'): JsonResponse
    {
        return $this->error(
            $message,
            RequestStatus::VALIDATION_ERROR,
            Response::HTTP_UNPROCESSABLE_ENTITY,
            ['errors' => $errors]
        );
    }

    /**
     * Return an authentication error response
     *
     * @param string $message
     * @return JsonResponse
     */
    protected function authenticationError(string $message = 'Authentication failed'): JsonResponse
    {
        return $this->error(
            $message,
            RequestStatus::AUTHENTICATION_ERROR,
            Response::HTTP_UNAUTHORIZED
        );
    }

    /**
     * Return a not found response
     *
     * @param string $message
     * @return JsonResponse
     */
    protected function notFound(string $message = 'Resource not found'): JsonResponse
    {
        return $this->error(
            $message,
            RequestStatus::NOTFOUND,
            Response::HTTP_NOT_FOUND
        );
    }

    /**
     * Return an empty response
     *
     * @return JsonResponse
     */
    protected function empty(): JsonResponse
    {
        return response()->json([
            RequestStatus::SUCCESS => true,
            RequestStatus::EMPTY => true
        ], Response::HTTP_OK);
    }
}
