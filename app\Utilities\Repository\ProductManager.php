<?php

namespace App\Utilities\Repository;

use App\Models\Product;
use App\Models\ProductCategory;
use Illuminate\Database\Eloquent\Builder;

class ProductManager
{
    /**
     * @param array|string $relations
     * @param array|string $count
     * @return Builder
     */
    public function all(array|string $relations = [], array|string $count = []): Builder
    {
        return Product::with($relations)->withCount($count)->orderBy('name', 'desc');
    }

    /**
     * @param array|string $relations
     * @param array|string $count
     * @return Builder
     */
    public function categories(array|string $relations = [], array|string $count = []): Builder
    {
        return ProductCategory::with($relations)->withCount($count)->orderBy('name', 'desc');
    }
}
