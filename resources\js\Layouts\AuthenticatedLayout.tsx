import ApplicationLogo from '@/Components/ApplicationLogo';
import Dropdown from '@/Components/Dropdown';
import Header from '@/Components/header';
import NavLink from '@/Components/NavLink';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink';
import { Link, usePage } from '@inertiajs/react';
import { PropsWithChildren, ReactNode, useState } from 'react';

export default function Authenticated({
    header,
    children,
}: PropsWithChildren<{ header?: ReactNode }>) {
    const user = usePage().props.auth.user;

    const [showingNavigationDropdown, setShowingNavigationDropdown] =
        useState(false);

    return (
        <div className="@container min-h-screen">
            <Header />

            {header && (
                <h3 className='px-5 @6xl:px-16 py-4 font-bold'>
                    {header}
                </h3>
            )}

            <main className='px-5 @6xl:px-16'>
                {children}
            </main>
        </div>
    );
}
