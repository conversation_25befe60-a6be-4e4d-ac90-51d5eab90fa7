<?php

namespace App\Utilities;

use Illuminate\Support\Str;

class StringModifier
{
    /**
     * @param $text
     * @param int $max
     * @param string $append
     * @return string
     */
    public static function ellipsis($text, $max = 100, $append = '&hellip;')
    {
        if(strlen($text) <= $max){
            return $text;
        }

        $out = substr($text, 0, $max);

        if(strpos($text,' ') === FALSE){
            return $out.$append;
        }

        return preg_replace('/\w+$/','',$out).$append;
    }

    /**
     * @param $string
     * @return string
     */
    public static function abbreviate($string)
    {
        $abbreviation = "";
        $string = ucwords($string);
        $words = explode(" ", "$string");

        foreach($words as $word){
            if($word){
                $abbreviation .= $word[0];
            }
        }

        return $abbreviation;
    }

    /**
     * @param $string
     * @param $person
     * @return string
     */
    public static function get_gender($string, int $person = 2)
    {
        switch ($person){
            case 1:
                $gender = $string == 'male' ? 'his' : 'her';
                break;
            case 2:
                $gender = $string == 'male' ? 'he' : 'she';
                break;
            default:
                $gender = 'their';
        }

        return $gender;
    }

    /**
     * @param $number
     * @return int|null|string
     */
    public static function validate_phone_number($number)
    {
        $number = str_replace('o', '0', str_replace('O', '0', $number));
        if(is_numeric($number)){
            if(!starts_with($number, 0)) {
                $number = '0' . $number;
            }
            if(in_array(substr($number, 0, 3), config('custom.smsprefixers'))){
                if(strlen($number) > 11 || strlen($number) == 10){
                    $phone = false;
                }
                else if(strlen($number) == 11){
                    $phone = $number;
                }
                else{
                    $phone = false;
                }
            }
            else{
                $phone = false;
            }
        }
        else{
            $phone = false;
        }

        return $phone;
    }

    /**
     * @param $phone
     * @return string
     */
    public static function format_to_international_number($phone): string
    {
        return Str::replaceFirst('0', '234', $phone);
    }

    /**
     * @param $email
     * @return bool
     */
    public static function isValidEmail($email)
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
}
