<?php

namespace App\Services\Api\Paystack;

class Transfers extends Paystack
{
    /**
     * @return array
     */
    public function get_banks(): array
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://api.paystack.co/bank",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => $this->http_header(),
        ));

        $result = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if ($err) {
            return ['status' => false, 'error' => "cURL Error: " . $err];
        } else {
            $response = json_decode($result, true);

            if (!$response['status']) {
                return ['status' => false, 'error' => $response['message']];
            } else {
                return ['status' => true, 'data' => $response['data']];
            }
        }
    }

    /**
     * @param $recipient
     * @return array
     */
    public function create_recipient($recipient): array
    {
        $url = "https://api.paystack.co/transferrecipient";

        $fields = [
            "type" => "nuban",
            "name" => $recipient['name'],
            "account_number" => $recipient['account_number'],
            "bank_code" => $recipient['bank_code'],
            "currency" => "NGN"
        ];

        $fields_string = http_build_query($fields);

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $fields_string);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->http_header());
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        $err = curl_error($ch);
        curl_close($ch);

        if ($err) {
            return ['status' => false, 'error' => "cURL Error: " . $err];
        } else {
            $response = json_decode($result, true);

            if (!$response['status']) {
                return ['status' => false, 'error' => $response['message']];
            } else {
                return ['status' => true, 'data' => $response['data']];
            }
        }
    }

    /**
     * @param $id
     * @return array
     */
    public function fetch_recipient($id): array
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://api.paystack.co/transferrecipient/$id",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => $this->http_header()
        ));

        $result = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if ($err) {
            return ['status' => false, 'error' => "cURL Error: " . $err];
        } else {
            $response = json_decode($result, true);

            if (!$response['status']) {
                return ['status' => false, 'error' => $response['message']];
            } else {
                return ['status' => true, 'data' => $response['data']];
            }
        }
    }

    /**
     * @param $recipient
     * @return array
     */
    public function update_recipient($recipient): array
    {
        $url = "https://api.paystack.co/transferrecipient/:id_or_code";

        $fields = [
            'name' => $recipient['name']
        ];

        $fields_string = http_build_query($fields);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $fields_string);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->http_header());

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        $err = curl_error($ch);

        curl_close($ch);

        if ($err) {
            return ['status' => false, 'error' => "cURL Error: " . $err];
        } else {
            $response = json_decode($result, true);

            if (!$response['status']) {
                return ['status' => false, 'error' => $response['message']];
            } else {
                return ['status' => true, 'data' => $response['data']];
            }
        }
    }

    /**
     * @param $recipient
     * @param $details
     * @return array
     */
    public function initiate_transfer($recipient, $details): array
    {
        $url = "https://api.paystack.co/transfer";

        $fields = [
            "source" => "balance",
            "reason" => $details['reason'],
            "amount" => $details['amount'],
            "recipient" => $recipient
        ];

        $fields_string = http_build_query($fields);

        $ch = curl_init();

        curl_setopt($ch,CURLOPT_URL, $url);
        curl_setopt($ch,CURLOPT_POST, true);
        curl_setopt($ch,CURLOPT_POSTFIELDS, $fields_string);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->http_header());

        curl_setopt($ch,CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        $err = curl_error($ch);

        curl_close($ch);

        if ($err) {
            return ['status' => false, 'error' => "cURL Error: " . $err];
        } else {
            $response = json_decode($result, true);

            if (!$response['status']) {
                return ['status' => false, 'error' => $response['message']];
            } else {
                return ['status' => true, 'data' => $response['data']];
            }
        }
    }

    /**
     * @param $transfer
     * @param $otp
     * @return array
     */
    public function finalize_transfer($transfer, $otp): array
    {
        $url = "https://api.paystack.co/transfer/finalize_transfer";

        $fields = [
            "transfer_code" => $transfer,
            "otp" => $otp
        ];

        $fields_string = http_build_query($fields);

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $fields_string);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->http_header());

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        $err = curl_error($ch);

        curl_close($ch);

        if ($err) {
            return ['status' => false, 'error' => "cURL Error: " . $err];
        } else {
            $response = json_decode($result, true);

            if (!$response['status']) {
                return ['status' => false, 'error' => $response['message']];
            } else {
                return ['status' => true, 'data' => $response['data']];
            }
        }

    }

    /**
     * @param array $transfers
     * @return array
     */
    public function bulk_transfer(array $transfers): array
    {
        $url = "https://api.paystack.co/transfer/bulk";
        $fields = [
            "currency" => "NGN",
            "source" => "balance",
            "transfers" => $transfers
        ];

        $fields_string = http_build_query($fields);

        $ch = curl_init();

        curl_setopt($ch,CURLOPT_URL, $url);
        curl_setopt($ch,CURLOPT_POST, true);
        curl_setopt($ch,CURLOPT_POSTFIELDS, $fields_string);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->http_header());

        curl_setopt($ch,CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        $err = curl_error($ch);

        curl_close($ch);

        if ($err) {
            return ['status' => false, 'error' => "cURL Error: " . $err];
        } else {
            $response = json_decode($result, true);

            if (!$response['status']) {
                return ['status' => false, 'error' => $response['message']];
            } else {
                return ['status' => true, 'data' => $response['data']];
            }
        }
    }

    /**
     * @return array
     */
    public function list_transfers(): array
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://api.paystack.co/transfer",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => $this->http_header()
        ));

        $result = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if ($err) {
            return ['status' => false, 'error' => "cURL Error: " . $err];
        } else {
            $response = json_decode($result, true);

            if (!$response['status']) {
                return ['status' => false, 'error' => $response['message']];
            } else {
                return ['status' => true, 'data' => $response['data']];
            }
        }
    }

    /**
     * @param $transfer_code
     * @return array
     */
    public function fetch_transfer($transfer_code): array
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://api.paystack.co/transfer/$transfer_code",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => $this->http_header()
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if ($err) {
            return ['status' => false, 'error' => "cURL Error: " . $err];
        } else {
            $response = json_decode($response, true);

            if (!$response['status']) {
                return ['status' => false, 'error' => $response['message']];
            } else {
                return ['status' => true, 'data' => $response['data']];
            }
        }
    }

    /**
     * @param $reference
     * @return array
     */
    public function verify_transfer($reference): array
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://api.paystack.co/transfer/verify/$reference",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => $this->http_header()
        ));

        $result = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if ($err) {
            return ['status' => false, 'error' => "cURL Error: " . $err];
        } else {
            $response = json_decode($result, true);

            if (!$response['status']) {
                return ['status' => false, 'error' => $response['message']];
            } else {
                return ['status' => true, 'data' => $response['data']];
            }
        }
    }

    /**
     * @return array
     */
    public function check_balance(): array
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://api.paystack.co/balance",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => $this->http_header()
        ));

        $result = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if ($err) {
            return ['status' => false, 'error' => "cURL Error: " . $err];
        } else {
            $response = json_decode($result, true);

            if (!$response['status']) {
                return ['status' => false, 'error' => $response['message']];
            } else {
                return ['status' => true, 'data' => $response['data']];
            }
        }
    }
}
