<?php

namespace App\Http\Controllers\Dashboard\Apartments;

use App\Http\Controllers\Controller;
use App\Models\Apartment;
use Inertia\Inertia;
use Illuminate\Http\Request;

class ApartmentsController extends Controller
{
    /**
     * ApartmentsController constructor.
     */
    public function __construct()
    {
        $this->model = new Apartment();
        $this->path = 'dashboard/apartments';
    }

    public function index()
    {
        return Inertia::render('properties/index');
    }
}
