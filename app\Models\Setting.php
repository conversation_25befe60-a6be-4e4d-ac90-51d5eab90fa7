<?php

namespace App\Models;

use App\Casts\JsonToObject;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    use HasFactory;

    /**
     * @var string[]
     */
    protected $casts = [
        'users' => JsonToObject::class,
        'owners' => JsonToObject::class,
        'affiliates' => JsonToObject::class,
        'payment_gateway' => JsonToObject::class,
        'send_sms' => 'bool',
        'send_email' => 'bool'
    ];
}
