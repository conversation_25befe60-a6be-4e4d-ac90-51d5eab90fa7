<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bookings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id');
            $table->foreignId('owner_id');
            $table->foreignId('apartment_id');
            $table->unsignedBigInteger('amount');
            $table->unsignedBigInteger('apartment_price');
            $table->dateTime('check_in_at');
            $table->dateTime('check_out_at');
            $table->unsignedInteger('days');
            $table->json('fees')->nullable();
            $table->unsignedInteger('guests');
            $table->string('status')->default('pending');
            $table->boolean('approved')->default(false);
            $table->text('approval_comment')->nullable();
            $table->boolean('rejected')->default(false);
            $table->string('rejection_comment')->nullable();
            $table->text('rejection_reason')->nullable();


            $table->string('code')->unique();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bookings');
    }
};
