<?php

namespace App\Utilities;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Validator;

class RequestValidation
{
    /**
     * @param Request $request
     * @param array $values
     * @return bool|JsonResponse
     */
    public static function validate(Request $request, array $values)
    {
        $validator = Validator::make($request->all(), $values);

        if($validator->fails()){
            return response()->json([RequestStatus::VALIDATION_ERROR => $validator->errors()], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        return true;
    }
}
