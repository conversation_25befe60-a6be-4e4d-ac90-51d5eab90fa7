<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('owner_payout_banks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('owner_id');
            $table->string('bank_name');
            $table->string('account_number');
            $table->string('account_name');
            $table->boolean('active')->default(true);
            $table->string('code')->unique();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('owner_payout_banks');
    }
};
