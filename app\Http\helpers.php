<?php

/**
 * This is my custom helper function
 * <PERSON><PERSON> keeps resetting it's global helper file
 * So I opened a new file of my own, damn you <PERSON><PERSON>!
 */

use App\Models\Legal;
use App\Models\Setting;
use App\Services\Api\Paystack\Paystack;
use App\Services\Email\EmailConfig;
use App\Utilities\Accent;
use App\Utilities\DateManipulator;
use App\Utilities\MediaUploader;
use App\Utilities\Repository\ProductManager;
use App\Utilities\Repository\UserManager;
use App\Utilities\NumbersManipulator;
use App\Utilities\RandomGenerator;
use App\Utilities\SMSSender;
use Illuminate\Support\Facades\Auth;
use App\Utilities\StringModifier;
use Illuminate\Contracts\Auth\Authenticatable;

if (! function_exists('number_manipulator')) {
    /**
     * @return NumbersManipulator
     */
    function number_manipulator(): NumbersManipulator
    {
        return new NumbersManipulator();
    }
}

if (! function_exists('generate_random_string')) {
    /**
     * @param $length
     * @return string
     * @throws Exception
     */
    function generate_random_string($length): string
    {
        return RandomGenerator::generate_random_string($length);
    }
}

if (! function_exists('generate_verification_code')) {
    /**
     * @param $length
     * @return string
     */
    function generate_verification_code($length): string
    {
        return RandomGenerator::generate_verification_code($length);
    }
}

if (! function_exists('generate_unique_uuid')) {
    /**
     * @return mixed
     */
    function generate_unique_uuid(): mixed
    {
        return RandomGenerator::generate_unique_uuid();
    }
}

if (! function_exists('generate_random_numbers')) {
    /**
     * @param $length
     * @return string
     */
    function generate_random_numbers($length): string
    {
        return RandomGenerator::generate_random_numbers($length);
    }
}

if (! function_exists('generate_ordinal_list')) {
    /**
     * @param $length
     * @return mixed
     */
    function generate_ordinal_list($length): mixed
    {
        return RandomGenerator::generate_ordinal_list($length);
    }
}

if (! function_exists('get_ordinal_suffix')) {
    /**
     * @param $number
     * @return string
     */
    function get_ordinal_suffix($number): string
    {
        return RandomGenerator::get_ordinal_suffix($number);
    }
}

if (! function_exists('time_ago')) {
    /**
     * @param $date
     * @return string
     * @throws Exception
     */
    function time_ago($date): string
    {
        return DateManipulator::time_ago($date);
    }
}

if (! function_exists('get_working_days_from_count')) {
    /**
     * @param $currentDate
     * @param $numBusDays
     * @param array $holidays
     * @param array $resultDates
     * @return array|mixed
     */
    function get_working_days_from_count($currentDate, $numBusDays, $holidays = array(), $resultDates = array()): mixed
    {
        return (new DateManipulator())->getWorkingDaysFromCount($currentDate, $numBusDays, $holidays = array(), $resultDates = array());
    }
}

if (! function_exists('date_percent_diff')) {
    /**
     * @param $date1
     * @param $date2
     * @return float|int
     */
    function date_percent_diff($date1, $date2): float|int
    {
        return DateManipulator::date_percent_diff($date1, $date2);
    }
}

if (! function_exists('get_working_days')) {
    /**
     * @param $date1
     * @param $date2
     * @return int
     */
    function get_working_days($date1, $date2): int
    {
        return DateManipulator::getWorkingDays($date1, $date2);
    }
}

if (! function_exists('list_working_days')) {
    /**
     * @param $date
     * @return array
     */
    function list_working_days($date): array
    {
        return DateManipulator::listWorkingDays($date);
    }
}

if (! function_exists('get_working_days_from_range')) {
    /**
     * @param $start
     * @param $end
     * @return mixed
     */
    function get_working_days_from_range($start, $end)
    {
        return DateManipulator::getWorkingDaysFromRange($start, $end);
    }
}

if (! function_exists('get_date_from_range')) {
    /**
     * @param $date1
     * @param $date2
     * @return array
     */
    function get_date_from_range($date1, $date2)
    {
        return DateManipulator::getDatesFromRange($date1, $date2);
    }
}

if (! function_exists('time_left')) {
    /**
     * @param $date
     * @return mixed
     */
    function time_left($date)
    {
        return DateManipulator::time_remaining($date);
    }
}

if (! function_exists('age')) {
    /**
     * @param $date
     * @return int
     */
    function age($date)
    {
        return DateManipulator::age($date);
    }
}

if (! function_exists('get_age')) {
    /**
     * @param $date
     * @return false|int|string
     */
    function get_age($date)
    {
        return DateManipulator::get_age($date);
    }
}

if (! function_exists('count_date_from_range')) {
    /**
     * @param $date1
     * @param $date2
     * @return int
     */
    function count_date_from_range($date1, $date2)
    {
        return DateManipulator::countDatesFromRange($date1, $date2);
    }
}

if (! function_exists('remove_accent')) {
    /**
     * @param $string
     * @return mixed
     */
    function remove_accent($string)
    {
        return Accent::remove_accent($string);
    }
}

if (! function_exists('ellipsis')) {
    /**
     * @param $string
     * @param $length
     * @return string
     */
    function ellipsis($string, $length)
    {
        return StringModifier::ellipsis($string, $length);
    }
}

if (! function_exists('abbreviate')) {
    /**
     * @param $string
     * @return string
     */
    function abbreviate($string)
    {
        return StringModifier::abbreviate($string);
    }
}

if (! function_exists('get_gender')) {
    /**
     * @param $string
     * @param int $person
     * @return string
     */
    function get_gender($string, int $person = 2)
    {
        return StringModifier::get_gender($string, $person);
    }
}

if (! function_exists('get_guard')) {
    /**
     * @return mixed
     */
    function get_guard()
    {
        return Auth::getDefaultDriver();
    }
}

if (! function_exists('me')) {
    /**
     * @return Authenticatable|null
     */
    function me(): Authenticatable|null
    {
        return auth()->guard(get_guard())->user();
    }
}

if (! function_exists('slug_reverse')) {
    /**
     * @param $string
     * @return string
     */
    function slug_reverse($string): string
    {
        return title_case(str_replace('-', ' ', $string));
    }
}

if (! function_exists('generate_unique_code')) {
    /**
     * @param $model
     * @param $length
     * @return string
     * @throws Exception
     */
    function generate_unique_code($model, $length): string
    {
        return RandomGenerator::generate_unique_code($model, $length);
    }
}

if (! function_exists('generate_unique_uuid')) {
    /**
     * @return string
     */
    function generate_unique_uuid(): string
    {
        return RandomGenerator::generate_unique_uuid();
    }
}

if (! function_exists('get_settings')) {
    /**
     * @return mixed
     */
    function get_settings(): mixed
    {
        return optional(Setting::first());
    }
}

if (! function_exists('get_legals')) {
    /**
     * @return mixed
     */
    function get_legals()
    {
        return optional(Legal::first());
    }
}

if (! function_exists('upload_path')) {
    /**
     * @return string
     */
    function upload_path()
    {
        return '/uploads/';
    }
}

if (! function_exists('sms')) {
    /**
     * @return SMSSender
     */
    function sms()
    {
        return new SMSSender();
    }
}

if (! function_exists('validate_phone_number')) {
    /**
     * @param $number
     * @return int|null|string
     */
    function validate_phone_number($number)
    {
        return StringModifier::validate_phone_number($number);
    }
}

if (! function_exists('is_email')) {
    /**
     * @param $string
     * @return bool
     */
    function is_email($string)
    {
        return StringModifier::isValidEmail($string);
    }
}

if(! function_exists('user_manager')) {
    /**
     * @return UserManager
     */
    function user_manager()
    {
        return new UserManager();
    }
}

if(! function_exists('file_uploader')) {
    /**
     * @return MediaUploader
     */
    function file_uploader(): MediaUploader
    {
        return new MediaUploader();
    }
}

if (!function_exists('format_to_international_number'))
{
    /**
     * @param $phone
     * @return string
     */
    function format_to_international_number($phone): string
    {
        return StringModifier::format_to_international_number($phone);
    }
}

if (!function_exists('percentage'))
{
    /**
     * @param $percentage
     * @param $number
     * @return float|int
     */
    function percentage($percentage, $number): float|int
    {
        return NumbersManipulator::percentage($percentage, $number);
    }
}

if (!function_exists('product_manager'))
{
    /**
     * @return ProductManager
     */
    function product_manager()
    {
        return new ProductManager();
    }
}

if (!function_exists('get_paystack'))
{
    /**
     * @return Paystack
     */
    function get_paystack(): Paystack
    {
        return new Paystack();
    }
}

if (! function_exists('email')) {
    /**
     * @return EmailConfig
     */
    function email(): EmailConfig
    {
        return new EmailConfig();
    }
}
