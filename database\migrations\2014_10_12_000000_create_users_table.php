<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email')->unique();
            $table->string('phone')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->text('suspension_comment')->nullable();
            $table->boolean('flagged')->default(false);
            $table->text('flagged_reason')->nullable();
            $table->json('bank_information')->nullable();
            $table->dateTime('send_next_otp_sms_after')->nullable();
            $table->string('password_reset_code')->nullable();
            $table->dateTime('last_login')->nullable();
            $table->string('password');
            $table->string('code')->unique();
            $table->softDeletes();
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
