<?php

namespace App\Utilities;

use Illuminate\Http\Response;

class WordsUtility
{
    /**
     * @param string|array $comment
     * @return array|false|string|string[]|null
     */
    public function censored_words(string | array $comment, $school = null)
    {
        $censored = [];

        if(is_array($comment)) {
            $comment = implode(' ', $comment);
        }

        if($censored){

            $words = is_array($censored->words) ? $censored->words : explode(', ', $censored->words);

            foreach ($words as $word) {
                if (stripos($comment, $word) !== false) {
                    if ($censored->behaviour === 'reject') {
                        return response()->json([RequestStatus::VALIDATION_ERROR => 'The presence of censored words, please review your input and try again'], Response::HTTP_NOT_FOUND);
                    }
                    elseif ($censored->behaviour === 'mask') {
                        $comment = preg_replace("/\b$word\b/i", str_repeat('*', strlen($word)), $comment);
                    }
                }
            }
        }

        return $comment;
    }
}
