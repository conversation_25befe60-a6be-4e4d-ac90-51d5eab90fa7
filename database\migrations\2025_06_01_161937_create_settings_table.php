<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->json('emails')->nullable();
            $table->json('phone')->nullable();
            $table->string('facebook')->nullable();
            $table->string('twitter')->nullable();
            // $table->string('twitter')->nullable();
            $table->string('instagram')->nullable();
            $table->string('youtube')->nullable();
            $table->string('tiktok')->nullable();
            $table->boolean('sms')->default(true);
            $table->boolean('email')->default(true);
            $table->json('payment_gateway')->nullable();
            $table->json('users')->nullable();
            $table->json('owners')->nullable();
            $table->json('affiliates')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
