<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class State extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'country_id',-
        'name',
        'key',
        'alias',
        'active',
    ];

    /**
     * @var string[]
     */
    protected $casts = [
        'active' => 'bool'
    ];

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * @return HasMany
     */
    public function cities()
    {
        return $this->hasMany(City::class);
    }

    /**
     * @return HasMany
     */
    public function apartments()
    {
        return $this->hasMany(Apartment::class, 'state_id');
    }
}
