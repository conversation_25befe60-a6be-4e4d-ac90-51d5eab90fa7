import { Link, router, usePage } from '@inertiajs/react';
import ApplicationLogo from './ApplicationLogo';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuPortal, DropdownMenuSub, DropdownMenuSubContent, DropdownMenuSubTrigger, DropdownMenuTrigger } from './ui/dropdown-menu';
import { AlignJustify, Bell, ChevronDown, Search, Settings } from 'lucide-react';
import { useInitials } from '@/hooks/useInititals';
import { SharedData } from '@/types';
import { useMobileNavigation } from '@/hooks/use-mobile-navigation';
import { NavigationMenu, NavigationMenuContent, NavigationMenuItem, NavigationMenuLink, NavigationMenuList, NavigationMenuTrigger } from './ui/navigation-menu';
import { useState } from 'react';
import { cn } from '@/lib/utils';
import { DropdownMenuArrow } from '@radix-ui/react-dropdown-menu';

const UserMenu = () => {
    const { url } = usePage()
    const [isOpen, setIsOpen] = useState(false);

    return (
        <DropdownMenu open={isOpen} onOpenChange={(isOpen) => setIsOpen(isOpen)} modal={false}>
            <DropdownMenuTrigger className='' onMouseEnter={() => setIsOpen(true)}>
                <div
                    className={`link cursor-pointer flex items-center gap-2 ${url.startsWith('/users') ? 'active' : ''}`}
                >
                    <p>Users</p>
                    <ChevronDown className={cn(isOpen ? 'rotate-180' : '', 'transition duration-200 h-5 w-5')} />
                </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent 
                hideWhenDetached
                align='end' 
                sideOffset={20} 
                className='w-60 py-4 px-6 flex flex-col gap-4 text-sm'
                onMouseLeave={() => setIsOpen(false)}
                onCloseAutoFocus={(e) => {
                    e.preventDefault();
                }}
            >
                <Link href={route('users.owner')}>
                    <DropdownMenuItem className={cn('cursor-pointer hover:!bg-transparent hover:!text-primary link', route().current('users.owner') ? 'active' : '')}>
                        Apartment Owner
                    </DropdownMenuItem>
                </Link>
                <Link href={route('users.customer')}>
                    <DropdownMenuItem className={cn('cursor-pointer hover:!bg-transparent hover:!text-primary link', route().current('users.customer') ? 'active' : '')}>
                        Customer Account
                    </DropdownMenuItem>
                </Link>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}

const RevenueMenu = () => {
    const { url } = usePage()
    const [isOpen, setIsOpen] = useState(false);

    return (
        <DropdownMenu open={isOpen} onOpenChange={(isOpen) => setIsOpen(isOpen)} modal={false}>
            <DropdownMenuTrigger className='' onMouseEnter={() => setIsOpen(true)}>
                <div
                    className={`link cursor-pointer flex items-center gap-2 ${url.startsWith('/revenues') ? 'active' : ''}`}
                >
                    <p>Revenue</p>
                    <ChevronDown className={cn(isOpen ? 'rotate-180' : '', 'transition duration-200 h-5 w-5')} />
                </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent 
                hideWhenDetached
                align='end' 
                sideOffset={20} 
                className='w-60 py-4 px-6 flex flex-col gap-4 text-sm'
                onMouseLeave={() => setIsOpen(false)}
                onCloseAutoFocus={(e) => {
                    e.preventDefault();
                }}
            >
                <Link href={route('revenues.overview')}>
                    <DropdownMenuItem className={cn('cursor-pointer hover:!bg-transparent hover:!text-primary link', route().current('revenues.overview') ? 'active' : '')}>
                        Revenue Overview
                    </DropdownMenuItem>
                </Link>
                <Link href={route('revenues.analytics')}>
                    <DropdownMenuItem className={cn('cursor-pointer hover:!bg-transparent hover:!text-primary link', route().current('revenues.analytics') ? 'active' : '')}>
                        User Analytics
                    </DropdownMenuItem>
                </Link>
                <Link href={route('revenues.performance')}>
                    <DropdownMenuItem className={cn('cursor-pointer hover:!bg-transparent hover:!text-primary link', route().current('revenues.performance') ? 'active' : '')}>
                        Performance Metrics
                    </DropdownMenuItem>
                </Link>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}





export default function Header() {
    const page = usePage<SharedData>();
    const url = page.url
    const cleanup = useMobileNavigation();
    const { auth } = page.props;
    const getInitials = useInitials();

    const handleLogout = () => {
        cleanup();
        router.flushAll();
    };
    
    return (
        <header className='z-20 bg-white sticky top-0 shadow-sm px-5 @6xl:px-16 py-4 flex items-center'>
            <div className='w-full flex items-center gap-4 justify-between'>
                <Link href={route('dashboard')}>
                    <ApplicationLogo className="block h-20 w-auto fill-current text-gray-800" />
                </Link>
                
                {/* Desktop Menu */}
                <div className='hidden @6xl:flex items-center gap-28'>
                    <nav>
                        <ul className='flex gap-10'>
                            <li>
                                <Link
                                    className={`link ${route().current('dashboard') ? 'active' : ''}`}
                                    href={route('dashboard')}
                                >
                                    Dashboard
                                </Link>
                            </li>
                            <li>
                                <Link
                                    className={`link ${route().current('properties') ? 'active' : ''}`}
                                    href={route('properties')}
                                >
                                    Properties
                                </Link>
                            </li>
                            <li>
                                <Link
                                    className={`link ${route().current('bookings') ? 'active' : ''}`}
                                    href={route('bookings')}
                                >
                                    Bookings
                                </Link>
                            </li>
                            <li>
                                <UserMenu />
                            </li>
                            <li>
                                <Link
                                    className={`link ${route().current('financials') ? 'active' : ''}`}
                                    href={route('financials')}
                                >
                                    Financials
                                </Link>
                            </li>
                            <li>
                                <RevenueMenu />
                            </li>
                        </ul>
                    </nav>
                    <nav className='border border-[#E2E2E2] rounded-4xl py-2 px-4 flex items-center gap-4'>
                        <Avatar className='h-10 w-10'>
                            <AvatarImage src={auth.admin?.avatar} alt={auth.admin?.name} />
                            <AvatarFallback className='bg-neutral-200 text-black'>
                                {getInitials(auth.admin?.name || '')}
                            </AvatarFallback>
                        </Avatar>

                        <Bell className='w-5 h-5 cursor-pointer' />
                        <Link href={route('settings')}>
                            <Settings className='w-5 h-5 cursor-pointer' />
                        </Link>
                        
                        <DropdownMenu>
                            <DropdownMenuTrigger className='cursor-pointer'>
                                <AlignJustify color='#292D32' />
                            </DropdownMenuTrigger>
                            <DropdownMenuContent 
                                hideWhenDetached
                                align='end' 
                                sideOffset={20} 
                                className='w-60 py-4 px-6 flex flex-col gap-4 text-sm'
                            >
                                <Link href={route('notifications')}>
                                    <DropdownMenuItem className='cursor-pointer hover:!bg-transparent hover:!text-primary'>Notification</DropdownMenuItem>
                                </Link>
                                <Link href={route('helpCenter')}>
                                    <DropdownMenuItem className='cursor-pointer hover:!bg-transparent hover:!text-primary'>Help Center</DropdownMenuItem>
                                </Link>
                                <DropdownMenuItem className='!text-[#E71348] hover:!bg-transparent'>
                                    <Link method="post" href={route('logout')} as="button" onClick={handleLogout} className='cursor-pointer'>
                                        Logout
                                    </Link>
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </nav>
                </div>

                {/* Mobile Menu */}
                <div className='flex @6xl:hidden items-center gap-4'>
                    <Search color='#292D32' className='cursor-pointer' />
                    <DropdownMenu>
                        <DropdownMenuTrigger className='cursor-pointer'>
                            <AlignJustify color='#292D32' />
                        </DropdownMenuTrigger>
                        <DropdownMenuContent 
                            hideWhenDetached
                            align='end' 
                            sideOffset={20} 
                            className='w-60 py-4 px-6 flex flex-col gap-4 text-sm'
                        >
                            <nav>
                                <ul className='flex flex-col gap-4'>
                                    <li>
                                        <Link
                                            className={`link ${route().current('dashboard') ? 'active' : ''}`}
                                            href={route('dashboard')}
                                        >
                                            Dashboard
                                        </Link>
                                    </li>
                                    <li>
                                        <Link
                                            className={`link ${route().current('properties') ? 'active' : ''}`}
                                            href={route('properties')}
                                        >
                                            Properties
                                        </Link>
                                    </li>
                                    <li>
                                        <Link
                                            className={`link ${route().current('bookings') ? 'active' : ''}`}
                                            href={route('bookings')}
                                        >
                                            Bookings
                                        </Link>
                                    </li>
                                    <li>
                                        <Link
                                            className={`link ${route().current('users.owner') ? 'active' : ''}`}
                                            href={route('users.owner')}
                                        >
                                            Users
                                        </Link>
                                    </li>
                                    <li>
                                        <DropdownMenuSub>
                                            <DropdownMenuSubTrigger 
                                                className={cn('p-0 link', url.startsWith('/users') ? 'active' : '')}
                                            >Users</DropdownMenuSubTrigger>
                                            <DropdownMenuPortal>
                                                <DropdownMenuSubContent>
                                                    <Link href={route('users.owner')}>
                                                        <DropdownMenuItem className={cn('cursor-pointer hover:!bg-transparent hover:!text-primary link', route().current('users.owner') ? 'active' : '')}>
                                                            Apartment Owner
                                                        </DropdownMenuItem>
                                                    </Link>
                                                    <Link href={route('users.customer')}>
                                                        <DropdownMenuItem className={cn('cursor-pointer hover:!bg-transparent hover:!text-primary link', route().current('users.customer') ? 'active' : '')}>
                                                            Customer Account
                                                        </DropdownMenuItem>
                                                    </Link>
                                                </DropdownMenuSubContent>
                                            </DropdownMenuPortal>
                                        </DropdownMenuSub>
                                    </li>
                                    <li>
                                        <Link
                                            className={`link ${route().current('financials') ? 'active' : ''}`}
                                            href={route('financials')}
                                        >
                                            Financials
                                        </Link>
                                    </li>
                                    <li>
                                        <DropdownMenuSub>
                                            <DropdownMenuSubTrigger 
                                                className={cn('p-0 link', url.startsWith('/revenues') ? 'active' : '')}
                                            >Revenue</DropdownMenuSubTrigger>
                                            <DropdownMenuPortal>
                                                <DropdownMenuSubContent>
                                                    <Link href={route('revenues.overview')}>
                                                        <DropdownMenuItem className={cn('cursor-pointer hover:!bg-transparent hover:!text-primary link', route().current('revenues.overview') ? 'active' : '')}>
                                                            Revenue Overview
                                                        </DropdownMenuItem>
                                                    </Link>
                                                    <Link href={route('revenues.analytics')}>
                                                        <DropdownMenuItem className={cn('cursor-pointer hover:!bg-transparent hover:!text-primary link', route().current('revenues.analytics') ? 'active' : '')}>
                                                            User Analytics
                                                        </DropdownMenuItem>
                                                    </Link>
                                                    <Link href={route('revenues.performance')}>
                                                        <DropdownMenuItem className={cn('cursor-pointer hover:!bg-transparent hover:!text-primary link', route().current('revenues.performance') ? 'active' : '')}>
                                                            Performance Metrics
                                                        </DropdownMenuItem>
                                                    </Link>
                                                </DropdownMenuSubContent>
                                            </DropdownMenuPortal>
                                        </DropdownMenuSub>
                                    </li>
                                    <li>
                                        <Link
                                            className={`link ${route().current('notifications') ? 'active' : ''}`}
                                            href={route('notifications')}
                                        >
                                            Notification
                                        </Link>
                                    </li>
                                    <li>
                                        <Link
                                            className={`link ${route().current('helpCenter') ? 'active' : ''}`}
                                            href={route('helpCenter')}
                                        >
                                            Help Center
                                        </Link>
                                    </li>
                                    <li className='!text-[#E71348]'>
                                        <Link method="post" href={route('logout')} as="button" onClick={handleLogout} className='cursor-pointer'>
                                            Logout
                                        </Link>
                                    </li>
                                </ul>
                            </nav>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>
        </header>
    )
}