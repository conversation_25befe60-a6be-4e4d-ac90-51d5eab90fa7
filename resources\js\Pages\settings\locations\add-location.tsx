import { <PERSON><PERSON> } from "@/Components/ui/button";
import {
    <PERSON>alog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/Components/ui/dialog";
import { Input } from "@/Components/ui/input";
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from "@/Components/ui/radio-group";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/Components/ui/select";
import { Textarea } from "@/Components/ui/textarea";
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "@/Components/ui/tooltip";
import { Country } from "@/types/models/country";
import {
    AddLocationFormData,
    LocationType,
    State,
} from "@/types/models/location";
import { useForm } from "@inertiajs/react";
import * as Flags from "country-flag-icons/react/3x2";
import { HelpCircle, Loader2Icon, Plus } from "lucide-react";
import { FormEventHandler, useState, useEffect } from "react";
import { toast } from "sonner";
import { z } from "zod";

type Props = {
    country?: Country;
    states?: State[];
};

// Validation schema
const addLocationSchema = z
    .object({
        country_id: z.number().min(1, "Country is required"),
        location_type: z.enum(["state", "city"], {
            required_error: "Location type is required",
        }),
        state_id: z.number().optional(),
        locations: z.string().min(1, "At least one location is required"),
    })
    .refine(
        (data) => {
            // If location type is city, state_id is required
            if (data.location_type === "city" && !data.state_id) {
                return false;
            }
            return true;
        },
        {
            message: "State selection is required when adding cities",
            path: ["state_id"],
        }
    );

export default function AddLocation({ country, states = [] }: Props) {
    const [open, setOpen] = useState<boolean>(false);
    const [clientErrors, setClientErrors] = useState<Record<string, string>>(
        {}
    );

    const { data, setData, post, processing, errors, reset, clearErrors } =
        useForm<AddLocationFormData>({
            country_id: country?.id || 0,
            location_type: "state",
            state_id: undefined,
            locations: "",
        });

    // Update country_id when country prop changes
    useEffect(() => {
        if (country?.id) {
            setData("country_id", country.id);
        }
    }, [country?.id]);

    // Client-side validation
    const validateForm = (): boolean => {
        try {
            addLocationSchema.parse(data);
            setClientErrors({});
            return true;
        } catch (error) {
            if (error instanceof z.ZodError) {
                const newErrors: Record<string, string> = {};
                error.errors.forEach((err) => {
                    if (err.path.length > 0) {
                        newErrors[err.path[0] as string] = err.message;
                    }
                });
                setClientErrors(newErrors);
            }
            return false;
        }
    };

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        post(route("settings.locations.store"), {
            onSuccess: () => {
                toast.success(
                    `${
                        data.location_type === "state" ? "States" : "Cities"
                    } added successfully`
                );
                setOpen(false);
                reset();
                setClientErrors({});
            },
            onError: (errors) => {
                console.error("Add location errors:", errors);
                const errorMessage =
                    (Object.values(errors)[0] as string) ||
                    `Failed to add ${
                        data.location_type === "state" ? "states" : "cities"
                    }`;
                toast.error(errorMessage);
            },
        });
    };

    const handleCancel = () => {
        setOpen(false);
        reset();
        setClientErrors({});
        clearErrors();
    };

    const handleLocationTypeChange = (value: LocationType) => {
        setData("location_type", value);
        // Clear state selection when switching to state mode
        if (value === "state") {
            setData("state_id", undefined);
        }
        // Clear client errors for state_id when changing location type
        if (clientErrors.state_id) {
            setClientErrors((prev) => {
                const { state_id, ...rest } = prev;
                return rest;
            });
        }
    };

    const FlagComponent = country
        ? Flags[country.iso as keyof typeof Flags]
        : null;
    const locationTypeLabel =
        data.location_type === "state" ? "States" : "Cities";
    const placeholderText =
        data.location_type === "state"
            ? "California, Texas, Florida"
            : "Los Angeles, Houston, Miami";

    return (
        <TooltipProvider>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogTrigger asChild>
                    <Button>
                        <Plus className="" />
                        Add New
                    </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[500px]">
                    <DialogHeader>
                        <DialogTitle>Add New Locations</DialogTitle>
                        <DialogDescription>
                            Add multiple {locationTypeLabel.toLowerCase()} to{" "}
                            {country?.name || "the selected country"} at once.
                        </DialogDescription>
                    </DialogHeader>

                    <form onSubmit={submit} className="space-y-6">
                        {/* Country Display Field */}
                        <div className="space-y-2">
                            <Label htmlFor="country" className='font-bold'>Country</Label>
                            <div className="relative">
                                <Input
                                    id="country"
                                    value={
                                        country
                                            ? `${country.name}`
                                            : "No country selected"
                                    }
                                    disabled
                                    className="pl-12"
                                />
                                {FlagComponent && (
                                    <div className="absolute left-3 top-1/2 -translate-y-1/2">
                                        <FlagComponent
                                            className="w-6 h-4"
                                            title={country?.name}
                                        />
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Location Type Selection */}
                        <div className="space-y-3">
                            <Label className='font-bold'>Location Type</Label>
                            <RadioGroup
                                value={data.location_type}
                                onValueChange={handleLocationTypeChange}
                                className="flex gap-6"
                            >
                                <div className="flex items-center space-x-2">
                                    <RadioGroupItem value="state" id="state" />
                                    <Label
                                        htmlFor="state"
                                        className="font-bold"
                                    >
                                        State
                                    </Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <RadioGroupItem value="city" id="city" />
                                    <Label
                                        htmlFor="city"
                                        className="font-bold"
                                    >
                                        City
                                    </Label>
                                </div>
                            </RadioGroup>
                            {(clientErrors.location_type ||
                                errors.location_type) && (
                                <p className="text-sm text-destructive">
                                    {clientErrors.location_type ||
                                        errors.location_type}
                                </p>
                            )}
                        </div>

                        {/* Conditional State Selection */}
                        {data.location_type === "city" && (
                            <div className="space-y-2">
                                <Label htmlFor="state_id" className='font-bold'>Select State</Label>
                                <Select
                                    value={data.state_id?.toString()}
                                    onValueChange={(value) =>
                                        setData("state_id", parseInt(value))
                                    }
                                >
                                    <SelectTrigger className='w-full'>
                                        <SelectValue placeholder="Choose a state" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {states?.length > 0 ? (
                                            <>
                                                {states.map((state) => (
                                                    <SelectItem
                                                        key={state.id}
                                                        value={state.id.toString()}
                                                    >
                                                        {state.name}
                                                    </SelectItem>
                                                ))}
                                            </>
                                        ) : (
                                            <p className="text-sm text-center p-4 text-muted-foreground">
                                                No states available, come back <br /> when you've added some states.
                                            </p>
                                        )}
                                    </SelectContent>
                                </Select>
                                {(clientErrors.state_id || errors.state_id) && (
                                    <p className="text-sm text-destructive">
                                        {clientErrors.state_id ||
                                            errors.state_id}
                                    </p>
                                )}
                            </div>
                        )}

                        {/* Bulk Input Field */}
                        <div className="space-y-2">
                            <div className="flex items-center gap-2">
                                <Label htmlFor="locations" className='font-bold'>
                                    {locationTypeLabel}
                                </Label>
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p className="max-w-xs">
                                            Enter comma-separated{" "}
                                            {locationTypeLabel.toLowerCase()} to
                                            add multiple locations at once
                                            (e.g., {placeholderText})
                                        </p>
                                    </TooltipContent>
                                </Tooltip>
                            </div>
                            <Textarea
                                id="locations"
                                placeholder={`Enter ${locationTypeLabel.toLowerCase()} separated by commas (e.g., ${placeholderText})`}
                                value={data.locations}
                                onChange={(e) =>
                                    setData("locations", e.target.value)
                                }
                                className="min-h-[100px]"
                                aria-invalid={
                                    !!(
                                        clientErrors.locations ||
                                        errors.locations
                                    )
                                }
                            />
                            {(clientErrors.locations || errors.locations) && (
                                <p className="text-sm text-destructive">
                                    {clientErrors.locations || errors.locations}
                                </p>
                            )}
                            <p className="text-xs text-muted-foreground">
                                Separate multiple{" "}
                                {locationTypeLabel.toLowerCase()} with commas.
                                Each will be added as a separate location.
                            </p>
                        </div>

                        {/* Form Actions */}
                        <div className="flex justify-end gap-3 pt-4">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={handleCancel}
                                disabled={processing}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                disabled={processing || !country}
                                className="min-w-[100px]"
                            >
                                {processing ? (
                                    <Loader2Icon className="animate-spin" />
                                ) : (
                                    `Add ${locationTypeLabel}`
                                )}
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>
        </TooltipProvider>
    );
}
