<?php

namespace App\Services\Api\Paystack;

class Paystack
{
    /**
     * @var array|null
     */
    public ?array $key = [];

    /**
     * Paystack constructor.
     */
    public function __construct()
    {
        $this->key = $this->getKeys();
    }

    /**
     * @return array|null
     */
    public function getKeys(): ?array
    {
        $settings = get_settings()->payment_gateway->paystack;

        if ($settings->status === 'active') {
            if ($settings->mode === 'live') {
                return [
                    'public_key' => $settings->live_public_key,
                    'secret_key' => $settings->live_secret_key
                ];
            } elseif ($settings->mode === 'test') {
                return [
                    'public_key' => $settings->test_public_key,
                    'secret_key' => $settings->test_secret_key
                ];
            }
        }

        return null;
    }

    /**
     * @return string[]
     */
    public function http_header(): array
    {
        $key = $this->key['secret_key'];

        return [
            "Authorization: Bearer $key",
            "Cache-Control: no-cache",
        ];
    }

    /**
     * @param $account_no
     * @param $bank_code
     */
    public function validate_account($account_no, $bank_code)
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://api.paystack.co/bank/resolve?account_number=.$account_no.&bank_code=.$bank_code.",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => $this->http_header()
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            echo "cURL Error #:" . $err;
        } else {
            echo $response;
        }
    }
}
