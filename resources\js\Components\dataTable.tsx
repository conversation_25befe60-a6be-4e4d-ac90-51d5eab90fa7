import {
    ColumnDef,
    flexRender,
    getCoreRowModel,
    getPaginationRowModel,
    useReactTable,
    PaginationState,
    RowSelectionState,
} from "@tanstack/react-table";

import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "./ui/table";
import { DataTablePagination } from "./dataTablePagination";
import { PaginatedData } from "@/types";
import { useState, useEffect } from "react";

interface DataTableProps<TData, TValue> {
    columns: ColumnDef<TData, TValue>[];
    data: TData[];
    manualPagination?: boolean;
    rowCount?: number;
    paginationData?: PaginatedData;
    onPaginationChange?: (pagination: PaginationState) => void;
    searchValue?: string;
    onSearchChange?: (search: string) => void;
    onSelectionChange?: (selectedRows: TData[]) => void;
}

export function DataTable<TData, TValue>({
    columns,
    data,
    manualPagination = true,
    rowCount,
    paginationData,
    onPaginationChange,
    searchValue,
    onSearchChange,
    onSelectionChange,
}: DataTableProps<TData, TValue>) {
    const [pagination, setPagination] = useState<PaginationState>({
        pageIndex: paginationData ? paginationData.current_page - 1 : 0,
        pageSize: paginationData ? paginationData.per_page : 10,
    });

    const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

    useEffect(() => {
        if (onPaginationChange) {
            onPaginationChange(pagination);
        }
    }, [pagination, onPaginationChange]);

    useEffect(() => {
        if (onSelectionChange) {
            const selectedRows = Object.keys(rowSelection)
                .filter((key) => rowSelection[key])
                .map((key) => data[parseInt(key)])
                .filter(Boolean);
            onSelectionChange(selectedRows);
        }
    }, [rowSelection, onSelectionChange, data]);

    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        manualPagination,
        rowCount: rowCount || paginationData?.total,
        state: {
            pagination,
            rowSelection,
        },
        onPaginationChange: setPagination,
        onRowSelectionChange: setRowSelection,
        enableRowSelection: true,
        pageCount: paginationData
            ? paginationData.last_page
            : Math.ceil((rowCount || 0) / pagination.pageSize),
    });

    return (
        <div className="flex flex-col gap-2 pb-6">
            <div className="rounded-3xl border">
                <Table>
                    <TableHeader className="bg-[#F2F2F2] border-b border-[#F2F2F2]">
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id} className="">
                                {headerGroup.headers.map((header) => {
                                    return (
                                        <TableHead
                                            key={header.id}
                                            className="px-3 py-5 !min-h-48 first:!rounded-tl-3xl last:!rounded-tr-3xl"
                                        >
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(
                                                      header.column.columnDef
                                                          .header,
                                                      header.getContext()
                                                  )}
                                        </TableHead>
                                    );
                                })}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {table.getRowModel().rows?.length ? (
                            table.getRowModel().rows.map((row) => (
                                <TableRow
                                    key={row.id}
                                    data-state={
                                        row.getIsSelected() && "selected"
                                    }
                                    className="border-[#F2F2F2]"
                                >
                                    {row.getVisibleCells().map((cell) => (
                                        <TableCell
                                            key={cell.id}
                                            className="p-3"
                                        >
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext()
                                            )}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell
                                    colSpan={columns.length}
                                    className="h-24 text-center"
                                >
                                    <div className="flex flex-col items-center gap-2">
                                        <div className="text-gray-500">
                                            {searchValue
                                                ? `No results found matching "${searchValue}"`
                                                : "No results found"}
                                        </div>
                                        {searchValue && (
                                            <div className="text-sm text-gray-400">
                                                Try adjusting your search terms
                                            </div>
                                        )}
                                    </div>
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>
            <DataTablePagination
                table={table}
                paginationData={paginationData}
            />
        </div>
    );
}
