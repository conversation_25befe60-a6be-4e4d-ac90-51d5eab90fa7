<?php

namespace App\Services\Api\Paystack;

class Transactions extends Paystack
{
    /**
     * @param $id
     * @return array
     */
    public function get_transaction($id): array
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://api.paystack.co/transaction/$id",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => $this->getKeys(),
        ));

        $result = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            return ['status' => false, 'error' => "cURL Error: " . $err];
        } else {
            $response = json_decode($result, true);

            if (!$response['status']) {
                return ['status' => false, 'error' => $response['message']];
            } else {
                return ['status' => true, 'data' => $response['data']];
            }
        }
    }

    /**
     * @param $reference
     * @return array
     */
    public function verify_transaction($reference): array
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://api.paystack.co/transaction/verify/$reference",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => $this->http_header(),
        ));

        $result = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            return ['status' => false, 'error' => "cURL Error: " . $err];
        } else {
            $response = json_decode($result, true);

            if (!$response['status']) {
                return ['status' => false, 'error' => $response['message']];
            } else {
                return ['status' => true, 'data' => $response['data']];
            }
        }
    }
}
