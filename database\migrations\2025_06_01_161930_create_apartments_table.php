<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('apartments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('owner_id');
            $table->foreignId('country_id');
            $table->foreignId('state_id');
            $table->foreignId('city_id');
            $table->string('apartment_type_id');
            $table->string('title');
            $table->json('owned');
            $table->text('description');
            $table->unsignedInteger('guests');
            $table->unsignedInteger('bedrooms');
            $table->unsignedInteger('beds');
            $table->unsignedInteger('bathrooms');
            $table->unsignedInteger('toilets');
            $table->json('features')->nullable();
            $table->json('photos')->nullable();
            $table->json('videos')->nullable();
            $table->unsignedBigInteger('price');
            $table->unsignedInteger('minimum_nights');
            $table->unsignedInteger('maximum_nights');
            $table->dateTime('scheduled_time')->nullable();
            $table->boolean('scheduled')->default(false);
            $table->text('address')->nullable();
            $table->string('status')->default('pending');
            $table->boolean('approved')->default(false);
            $table->text('approval_comment')->nullable();
            $table->boolean('rejected')->default(false);
            $table->string('rejection_comment')->nullable();
            $table->text('rejection_reason')->nullable();
            $table->boolean('request_change')->default(false);
            $table->string('request_change_comment')->nullable();
            $table->text('request_change_reason')->nullable();
            $table->string('slug')->unique();
            $table->string('code')->unique();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('apartments');
    }
};
