import { Table } from "@tanstack/react-table";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useMemo } from "react";

import { Button } from "./ui/button";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "./ui/select";
import { PaginatedData } from "@/types";

const DOTS = "...";

const range = (start: number, end: number) => {
    let length = end - start + 1;
    return Array.from({ length }, (_, idx) => idx + start);
};

function generatePagination(
    totalCount: number,
    pageSize: number,
    siblingCount = 1,
    currentPage: number // 1-indexed current page
): (string | number)[] {
    const totalPageCount = Math.ceil(totalCount / pageSize);

    const totalPageNumbers = siblingCount + 5;

    if (totalPageNumbers >= totalPageCount) {
        return range(1, totalPageCount);
    }

    const leftSiblingIndex = Math.max(currentPage - siblingCount, 1);
    const rightSiblingIndex = Math.min(
        currentPage + siblingCount,
        totalPageCount
    );

    const shouldShowLeftDots = leftSiblingIndex > 2;
    const shouldShowRightDots = rightSiblingIndex < totalPageCount - 2;

    const firstPageIndex = 1;
    const lastPageIndex = totalPageCount;

    if (!shouldShowLeftDots && shouldShowRightDots) {
        let leftItemCount = 3 + 2 * siblingCount;
        let leftRange = range(1, leftItemCount);
        return [...leftRange, DOTS, totalPageCount];
    }

    if (shouldShowLeftDots && !shouldShowRightDots) {
        let rightItemCount = 3 + 2 * siblingCount;
        let rightRange = range(
            totalPageCount - rightItemCount + 1,
            totalPageCount
        );
        return [firstPageIndex, DOTS, ...rightRange];
    }

    if (shouldShowLeftDots && shouldShowRightDots) {
        let middleRange = range(leftSiblingIndex, rightSiblingIndex);
        return [firstPageIndex, DOTS, ...middleRange, DOTS, lastPageIndex];
    }

    return range(1, totalPageCount); // Fallback
}

interface DataTablePaginationProps<TData> {
    table: Table<TData>;
    paginationData?: PaginatedData;
}

export function DataTablePagination<TData>({
    table,
    paginationData,
}: DataTablePaginationProps<TData>) {
    const { pageIndex, pageSize } = table.getState().pagination;
    const totalItems = paginationData
        ? paginationData.total
        : table.getFilteredRowModel().rows.length;
    const pageCount = table.getPageCount();
    const currentPageForLogic = pageIndex + 1; // 1-indexed

    const paginationRange = useMemo(() => {
        if (totalItems === 0 || pageSize === 0 || pageCount === 0) return [];
        return generatePagination(totalItems, pageSize, 1, currentPageForLogic);
    }, [totalItems, pageSize, currentPageForLogic, pageCount]);

    if (pageCount === 0 && !paginationData?.total) { 
        // Show a minimal UI if no items and no server pagination data
        return (
            <div className="flex items-center justify-between px-2 py-4">
                <div className="text-muted-foreground text-sm">
                    No results
                </div>
                <div className="flex items-center space-x-2">
                    <p className="text-sm font-medium">Items per page</p>
                    <Select
                        value={`${pageSize}`}
                        onValueChange={(value) => {
                            table.setPageSize(Number(value));
                        }}
                    >
                        <SelectTrigger className="h-8 w-[70px]">
                            <SelectValue placeholder={`${pageSize}`} />
                        </SelectTrigger>
                        <SelectContent side="top">
                            {[10, 20, 25, 30, 40, 50].map((ps) => (
                                <SelectItem key={ps} value={`${ps}`}>
                                    {ps}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>
            </div>
        );
    }

    return (
        <div className="flex items-center justify-between px-2 py-4">
            <div className="text-muted-foreground text-sm">
                {paginationData ? (
                    <>
                        Showing {paginationData.from} - {paginationData.to} of{" "}
                        {paginationData.total} results
                    </>
                ) : (
                    <>
                        Showing{" "}
                        {pageIndex * pageSize + 1}
                        {" "}-{" "}
                        {Math.min((pageIndex + 1) * pageSize, totalItems)}{" "}
                        of {totalItems} results
                    </>
                )}
            </div>

            <div className="flex items-center space-x-1">
                <Button
                    variant="outline"
                    className="h-8 w-8 p-0 rounded-md"
                    onClick={() => table.previousPage()}
                    disabled={!table.getCanPreviousPage()}
                >
                    <span className="sr-only">Go to previous page</span>
                    <ChevronLeft className="h-4 w-4" />
                </Button>

                {paginationRange.map((pageNumber, idx) => {
                    if (pageNumber === DOTS) {
                        return (
                            <span
                                key={`${DOTS}-${idx}`}
                                className="flex h-8 w-8 items-center justify-center text-sm"
                            >
                                {DOTS}
                            </span>
                        );
                    }
                    const pageNumForTable = (pageNumber as number) - 1;
                    return (
                        <Button
                            key={pageNumber}
                            variant={pageIndex === pageNumForTable ? "default" : "ghost"}
                            className="h-8 w-8 p-0 rounded-lg"
                            onClick={() => table.setPageIndex(pageNumForTable)}
                        >
                            {pageNumber}
                        </Button>
                    );
                })}

                <Button
                    variant="outline"
                    className="h-8 w-8 p-0 rounded-md"
                    onClick={() => table.nextPage()}
                    disabled={!table.getCanNextPage()}
                >
                    <span className="sr-only">Go to next page</span>
                    <ChevronRight className="h-4 w-4" />
                </Button>
            </div>

            <div className="flex items-center space-x-2">
                <p className="text-sm font-medium">Items per page</p>
                <Select
                    value={`${pageSize}`}
                    onValueChange={(value) => {
                        table.setPageSize(Number(value));
                    }}
                >
                    <SelectTrigger className="h-8 w-[70px]">
                        <SelectValue placeholder={`${pageSize}`} />
                    </SelectTrigger>
                    <SelectContent side="top">
                        {[10, 20, 25, 30, 40, 50].map((ps) => (
                            <SelectItem key={ps} value={`${ps}`}>
                                {ps}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            </div>
        </div>
    );
}
