<?php

namespace App\Http\Controllers\Dashboard\Bookings;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use Inertia\Inertia;
use Illuminate\Http\Request;

class BookingController extends Controller
{
    /**
     * BookingController constructor.
     */
    public function __construct()
    {
        $this->model = new Booking();
        $this->path = 'dashboard/bookings';
    }

    public function index()
    {
        return Inertia::render($this->path.'/index');
    }
}
