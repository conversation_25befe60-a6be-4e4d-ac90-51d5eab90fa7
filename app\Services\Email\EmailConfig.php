<?php

namespace App\Services\Email;

use App\Utilities\RequestStatus;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use PHP<PERSON>ailer\PHPMailer\Exception;
use PHP<PERSON>ailer\PHPMailer\PHPMailer;

class EmailConfig
{
    /**
     * @param $to
     * @param $subject
     * @param $mail
     * @return bool|JsonResponse
     * @throws Exception
     */
    public function send_email($to, $subject, $mail): JsonResponse|bool
    {
        return self::php_mailer($to, $subject, $mail);
    }

    /**
     * @param $to
     * @param $subject
     * @param string $mail
     * @return bool
     */
    public static function php_mail($to, $subject, string $mail): bool
    {
        $from = "<EMAIL>";

        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= "From: Shortlet Africa <" . $from . ">\r\n";

        return @mail($to, $subject, $mail, $headers);
    }

    /**
     * @param $to
     * @param $subject
     * @param $body
     * @return false|JsonResponse
     * @throws Exception
     */
    public static function php_mailer($to, $subject, $body): bool|JsonResponse
    {
        if(!get_settings()->send_email){
            return false;
        }

        $mail = new PHPMailer(true);

        $mail->SMTPDebug = 0;
        $mail->isSMTP();
        $mail->Host = 'shortlet.africa';
        $mail->SMTPAuth = true;
        $mail->Username = '<EMAIL>';
        $mail->Password = '@ShortletAfrica';
        $mail->SMTPSecure = 'ssl';
        $mail->SMTPOptions = [
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            ]
        ];
        $mail->Port = 587;

        $mail->setFrom('<EMAIL>', config('app.name'));
        $mail->addAddress($to);

        if(isset($_FILES['emailAttachments'])) {
            for ($i=0; $i < count($_FILES['emailAttachments']['tmp_name']); $i++) {
                $mail->addAttachment($_FILES['emailAttachments']['tmp_name'][$i], $_FILES['emailAttachments']['name'][$i]);
            }
        }

        $mail->isHTML(true);

        $mail->Subject = $subject;
        $mail->Body = $body;

        $mail->addReplyTo('<EMAIL>', $to);

        if(!$mail->send()){
            return response()->json([RequestStatus::ERROR => 'Something went wrong with our servers, we will fix it soon'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json([RequestStatus::SUCCESS => 'Email has been sent successfully'], Response::HTTP_OK);
    }
}
