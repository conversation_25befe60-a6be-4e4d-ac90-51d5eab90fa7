<?php

namespace App\Http\Controllers\Dashboard;

use Exception;
use Inertia\Inertia;
use App\Models\Country;
use App\Models\Setting;
use Illuminate\Http\Request;
use App\Utilities\ThrowException;
use App\Http\Controllers\Controller;

class SettingsController extends Controller
{
    /**
     * SettingsController constructor.
     */
    public function __construct()
    {
        $this->model = new Setting();
        $this->path = 'settings';
    }

    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 10);
        $page = $request->get('page', 1);
        $search = $request->get('search', '');

        $query = Country::withTrashed();

        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('iso', 'LIKE', "%{$search}%")
                  ->orWhere('currency', 'LIKE', "%{$search}%")
                  ->orWhere('phonecode', 'LIKE', "%{$search}%")
                  ->orWhere('code', 'LIKE', "%{$search}%");
            });
        }

        $countries = $query->paginate($perPage, ['*'], 'page', $page);

        // Append search parameter to pagination links
        $countries->appends($request->only(['search', 'per_page']));

        return Inertia::render($this->path.'/index', [
            'countries' => $countries,
            'filters' => [
                'search' => $search,
            ]
        ]);
    }

    /**
     * View a specific country
     */
    public function viewCountry(Request $request, $id)
    {
        try {
            $country = Country::withTrashed()->findOrFail($id);

            return Inertia::render($this->path.'/locations/view-country', [
                'country' => $country,
            ]);
        } catch (Exception $exception) {
            return (new ThrowException())->throw($exception, $request);
        }
    }

    /**
     * Bulk suspend countries (soft delete)
     */
    public function bulkSuspend(Request $request)
    {
        $request->validate([
            'country_ids' => 'required|array|min:1',
            'country_ids.*' => 'integer|exists:countries,id'
        ]);

        try {
            $countryIds = $request->input('country_ids');

            // Get countries with trashed to check their current state
            $countries = Country::withTrashed()->whereIn('id', $countryIds)->get();

            $suspendedCount = 0;
            $restoredCount = 0;

            foreach ($countries as $country) {
                if ($country->deleted_at) {
                    // Country is suspended, restore it
                    $country->restore();
                    $restoredCount++;
                } else {
                    // Country is active, suspend it
                    $country->delete();
                    $suspendedCount++;
                }
            }

            if ($suspendedCount === 0 && $restoredCount === 0) {
                return back()->withErrors([
                    'bulk_suspend' => 'No changes were made to the selected countries.'
                ]);
            }

            // Create appropriate message
            $messages = [];
            if ($suspendedCount > 0) {
                $messages[] = "suspended {$suspendedCount} " . ($suspendedCount === 1 ? 'country' : 'countries');
            }
            if ($restoredCount > 0) {
                $messages[] = "restored {$restoredCount} " . ($restoredCount === 1 ? 'country' : 'countries');
            }

            $message = "Successfully " . implode(' and ', $messages) . '.';

        } catch (Exception $exception) {
            return (new ThrowException())->throw($exception, $request);
        }

        return back()->with([
            'flash' => [
                'success' => true,
                'message' => $message,
                'suspended_count' => $suspendedCount,
                'restored_count' => $restoredCount
            ]
        ]);
    }

    /**
     * Restore a single country (undo soft delete)
     */
    public function restoreCountry(Request $request, $id)
    {
        try {
            $country = Country::withTrashed()->findOrFail($id);

            if (!$country->deleted_at) {
                return back()->withErrors([
                    'restore' => 'Country is already active.'
                ]);
            }

            $country->restore();

        } catch (Exception $exception) {
            return (new ThrowException())->throw($exception, $request);
        }

        return back()->with([
            'flash' => [
                'success' => true,
                'message' => "Successfully restored {$country->name}.",
            ]
        ]);
    }
}
