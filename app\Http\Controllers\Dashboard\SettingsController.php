<?php

namespace App\Http\Controllers\Dashboard;

use Exception;
use Inertia\Inertia;
use App\Models\City;
use App\Models\Country;
use App\Models\Setting;
use App\Models\State;
use Illuminate\Http\Request;
use App\Utilities\ThrowException;
use App\Http\Controllers\Controller;
use Illuminate\Support\Str;

class SettingsController extends Controller
{
    /**
     * SettingsController constructor.
     */
    public function __construct()
    {
        $this->model = new Setting();
        $this->path = 'settings';
    }

    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 10);
        $page = $request->get('page', 1);
        $search = $request->get('search', '');

        $query = Country::withTrashed();

        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('iso', 'LIKE', "%{$search}%")
                  ->orWhere('currency', 'LIKE', "%{$search}%")
                  ->orWhere('phonecode', 'LIKE', "%{$search}%")
                  ->orWhere('code', 'LIKE', "%{$search}%");
            });
        }

        $countries = $query->paginate($perPage, ['*'], 'page', $page);

        // Append search parameter to pagination links
        $countries->appends($request->only(['search', 'per_page']));

        return Inertia::render($this->path.'/index', [
            'countries' => $countries,
            'filters' => [
                'search' => $search,
            ]
        ]);
    }

    /**
     * View a specific country
     */
    public function viewCountry(Request $request, $id)
    {
        try {
            $country = Country::withTrashed()->findOrFail($id);
            $states = State::where('active', true)->orderBy('name')->get();

            return Inertia::render($this->path.'/locations/view-country', [
                'country' => $country,
                'states' => $states,
            ]);
        } catch (Exception $exception) {
            return (new ThrowException())->throw($exception, $request);
        }
    }

    /**
     * Store multiple locations (states or cities)
     */
    public function storeLocations(Request $request)
    {
        $request->validate([
            'country_id' => 'required|integer|exists:countries,id',
            'location_type' => 'required|in:state,city',
            'state_id' => 'required_if:location_type,city|integer|exists:states,id',
            'locations' => 'required|string|min:1',
        ]);

        try {
            $country = Country::findOrFail($request->country_id);
            $locationType = $request->location_type;
            $locationsString = $request->locations;

            // Parse comma-separated locations
            $locationNames = array_map('trim', explode(',', $locationsString));
            $locationNames = array_filter($locationNames); 

            if (empty($locationNames)) {
                return back()->withErrors([
                    'locations' => 'Please provide at least one location name.'
                ]);
            }

            $createdCount = 0;
            $duplicateCount = 0;
            $errors = [];

            foreach ($locationNames as $locationName) {
                if (empty($locationName)) continue;

                try {
                    if ($locationType === 'state') {
                        // Create state
                        $key = Str::slug($locationName);
                        $alias = Str::slug($locationName);

                        // Check if state already exists
                        $existingState = State::where('name', $locationName)
                            ->orWhere('key', $key)
                            ->orWhere('alias', $alias)
                            ->first();

                        if ($existingState) {
                            $duplicateCount++;
                            continue;
                        }

                        State::create([
                            'country_id' => $country->id,
                            'name' => $locationName,
                            'key' => $country->iso . '-' . $key,
                            'alias' => $alias,
                            'active' => true,
                        ]);
                        $createdCount++;
                    } else {
                        // Create city
                        $slug = Str::slug($locationName);
                        $code = Str::slug($locationName, '_');

                        // Check if city already exists in the same state
                        $existingCity = City::where('name', $locationName)
                            ->where('state_id', $request->state_id)
                            ->first();

                        if ($existingCity) {
                            $duplicateCount++;
                            continue;
                        }

                        City::create([
                            'name' => $locationName,
                            'state_id' => $request->state_id,
                            'slug' => $slug,
                            'code' => $code,
                            'active' => true,
                        ]);
                        $createdCount++;
                    }
                } catch (Exception $e) {
                    $errors[] = "Failed to create {$locationName}: " . $e->getMessage();
                }
            }

            // Prepare success message
            $message = "Successfully created {$createdCount} " . ($locationType === 'state' ? 'states' : 'cities');
            if ($duplicateCount > 0) {
                $message .= ". {$duplicateCount} " . ($locationType === 'state' ? 'states' : 'cities') . " already existed and were skipped.";
            }

            if (!empty($errors)) {
                $message .= " Some locations failed to create: " . implode(', ', $errors);
            }

            return back()->with([
                'flash' => [
                    'success' => true,
                    'message' => $message,
                ]
            ]);

        } catch (Exception $exception) {
            return (new ThrowException())->throw($exception, $request);
        }
    }

    /**
     * Bulk suspend countries (soft delete)
     */
    public function bulkSuspend(Request $request)
    {
        $request->validate([
            'country_ids' => 'required|array|min:1',
            'country_ids.*' => 'integer|exists:countries,id'
        ]);

        try {
            $countryIds = $request->input('country_ids');

            // Get countries with trashed to check their current state
            $countries = Country::withTrashed()->whereIn('id', $countryIds)->get();

            $suspendedCount = 0;
            $restoredCount = 0;

            foreach ($countries as $country) {
                if ($country->deleted_at) {
                    // Country is suspended, restore it
                    $country->restore();
                    $restoredCount++;
                } else {
                    // Country is active, suspend it
                    $country->delete();
                    $suspendedCount++;
                }
            }

            if ($suspendedCount === 0 && $restoredCount === 0) {
                return back()->withErrors([
                    'bulk_suspend' => 'No changes were made to the selected countries.'
                ]);
            }

            // Create appropriate message
            $messages = [];
            if ($suspendedCount > 0) {
                $messages[] = "suspended {$suspendedCount} " . ($suspendedCount === 1 ? 'country' : 'countries');
            }
            if ($restoredCount > 0) {
                $messages[] = "restored {$restoredCount} " . ($restoredCount === 1 ? 'country' : 'countries');
            }

            $message = "Successfully " . implode(' and ', $messages) . '.';

        } catch (Exception $exception) {
            return (new ThrowException())->throw($exception, $request);
        }

        return back()->with([
            'flash' => [
                'success' => true,
                'message' => $message,
                'suspended_count' => $suspendedCount,
                'restored_count' => $restoredCount
            ]
        ]);
    }

    /**
     * Restore a single country (undo soft delete)
     */
    public function restoreCountry(Request $request, $id)
    {
        try {
            $country = Country::withTrashed()->findOrFail($id);

            if (!$country->deleted_at) {
                return back()->withErrors([
                    'restore' => 'Country is already active.'
                ]);
            }

            $country->restore();

        } catch (Exception $exception) {
            return (new ThrowException())->throw($exception, $request);
        }

        return back()->with([
            'flash' => [
                'success' => true,
                'message' => "Successfully restored {$country->name}.",
            ]
        ]);
    }
}
