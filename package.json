{"private": true, "type": "module", "scripts": {"build": "tsc && vite build", "dev": "vite"}, "devDependencies": {"@headlessui/react": "^2.0.0", "@inertiajs/react": "^2.0.0", "@tanstack/eslint-plugin-query": "^5.78.0", "@types/node": "^18.13.0", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.10", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.12", "axios": "^1.7.4", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.31", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.0.2", "vite": "^6.0.11"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-query": "^5.80.6", "@tanstack/react-query-devtools": "^5.80.6", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "country-flag-icons": "^1.5.19", "lucide-react": "^0.513.0", "next-themes": "^0.4.6", "react-hook-form": "^7.57.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8", "tw-animate-css": "^1.3.4", "zod": "^3.25.55"}}