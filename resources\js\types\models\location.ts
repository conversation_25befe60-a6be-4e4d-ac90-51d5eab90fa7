export interface State {
    id: number;
    name: string;
    key: string;
    alias: string;
    active: boolean;
    created_at: string;
    updated_at: string;
}

export interface City {
    id: number;
    name: string;
    state_id: number;
    slug: string;
    active: boolean;
    code: string;
    created_at: string;
    updated_at: string;
}

export type LocationType = "state" | "city";

export interface AddLocationFormData {
    country_id: number;
    location_type: LocationType;
    state_id?: number;
    locations: string;
}
