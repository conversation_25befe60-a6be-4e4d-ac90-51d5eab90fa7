<?php

namespace App\Utilities;

class RequestStatus
{
    public const VALIDATION_ERROR = "VALIDATION_ERROR";
    public const REQUEST_ERROR = "REQUEST_ERROR";
    public const REQUEST_INVALID = "REQUEST_INVALID";
    public const AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR";
    public const UNAUTHENTICATED = "UNAUTHENTICATED";
    public const ERROR = "ERROR";
    public const MESSAGE = "MESSAGE";

    public const SUCCESS = "SUCCESS";
    public const DATA = "DATA";
    public const EMPTY = "EMPTY";
    public const NOTFOUND = "NOTFOUND";
}
