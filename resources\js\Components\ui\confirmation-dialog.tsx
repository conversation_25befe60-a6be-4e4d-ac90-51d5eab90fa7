import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "./alert-dialog";

type Variant = 'default' | 'destructive' | 'warning';

interface ConfirmationDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    title: string;
    description: string;
    confirmText?: string;
    cancelText?: string;
    onConfirm: () => void;
    onCancel?: () => void;
    variant?: Variant;
    loading?: boolean;
}

const Icon = ({ variant }: { variant: Variant }) => {
    switch (variant) {
        case "warning":
            return (
                <img 
                    src="/icons/shield-cross.svg" 
                    alt="" 
                    className='p-4 bg-[#FD8C1B] rounded-full'
                />
            )
        default:
            return null
    }
}

const buttonColors = (variant: Variant) => {
    switch (variant) {
        case "destructive":
            return "bg-red-600 hover:bg-red-700 focus:ring-red-600"
        case "warning":
            return "bg-amber-600 hover:bg-amber-700 focus:ring-amber-600"
        default:
            return "bg-primary hover:bg-primary/90 focus:ring-primary"
    }
} 

export function ConfirmationDialog({
    open,
    onOpenChange,
    title,
    description,
    confirmText = "Confirm",
    cancelText = "Cancel",
    onConfirm,
    onCancel,
    variant = "default",
    loading = false,
}: ConfirmationDialogProps) {
    const handleCancel = () => {
        if (onCancel) {
            onCancel();
        }
        onOpenChange(false);
    };

    const handleConfirm = () => {
        onConfirm();
    };

    return (
        <AlertDialog open={open} onOpenChange={onOpenChange}>
            <AlertDialogContent className='rounded-4xl'>
                <AlertDialogHeader>
                    <AlertDialogTitle className='text-center flex flex-col gap-8'>
                        <div className='flex justify-center'>
                            <Icon variant={variant} />
                        </div>
                        {title}
                    </AlertDialogTitle>
                    <AlertDialogDescription className='text-center'>
                        {description}
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel
                        onClick={handleCancel}
                        disabled={loading}
                    >
                        {cancelText}
                    </AlertDialogCancel>
                    <AlertDialogAction
                        onClick={handleConfirm}
                        disabled={loading}
                        className={
                            buttonColors(variant)
                        }
                    >
                        {loading ? "Processing..." : confirmText}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
}
