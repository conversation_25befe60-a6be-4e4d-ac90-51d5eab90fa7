import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/Components/ui/tabs';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { Head } from '@inertiajs/react';
import Locations from './locations';


export default function Settings() {
    return (
        <AuthenticatedLayout header='Admin Settings'>
            <Head title="Settings" />

            <Tabs defaultValue="locations" className="mt-4">
                <TabsList>
                    <TabsTrigger value="general">General</TabsTrigger>
                    <TabsTrigger value="roles">Roles & Permissions</TabsTrigger>
                    <TabsTrigger value="data-reports">Data & Reports</TabsTrigger>
                    <TabsTrigger value="security">Security</TabsTrigger>
                    <TabsTrigger value="locations">Location Management</TabsTrigger>
                </TabsList>
                <TabsContent value="general">Make changes to your account here.</TabsContent>
                <TabsContent value="roles">Make changes to your account here.</TabsContent>
                <TabsContent value="data-reports">Make changes to your account here.</TabsContent>
                <TabsContent value="security">Make changes to your account here.</TabsContent>
                <TabsContent value="locations">
                    <Locations className='mt-6' />
                </TabsContent>
            </Tabs>
        </AuthenticatedLayout>
    )
}