<?php

namespace App\Utilities\Repository;

use App\Models\User;
use App\Staff;
use App\Vendor;
use App\Rider;
use App\Utilities\QueryStatus;
use App\Utilities\RequestStatus;
use Illuminate\Http\Response;

class UserManager
{
    /**
     * @param string $userType
     * @return string|null
     */
    public static function getModelClass(string $userType): ?string
    {
        $userType = strtolower($userType);

        return match ($userType) {
            'rider' => Rider::class,
            'user' => User::class,
            'vendor' => Vendor::class,
            'staff' => Staff::class,
            default => null
        };
    }

    /**
     * @return bool
     */
    public static function matchClass(): bool
    {
        $userType = get_class(me());

        return match ($userType) {
            'App\User', 'App\Rider', 'App\Staff', 'App\Vendor' => true,
            default => null
        };
    }

    /**
     * @param null $who
     * @return mixed
     */
    public static function getClass($who = null): mixed
    {
        $class = get_class($who ?? me());
        return (new $class);
    }

    /**
     * @param null $who
     * @return string|null
     */
    public static function getId($who = null): ?string
    {
        $class = get_class($who ?? me());

        $idMap = [
            'App\User' => 'user_id',
            'App\Rider' => 'user_id',
            'App\Vendor' => 'vendor_id',
            'App\Staff' => 'staff_id',
        ];

        return $idMap[$class] ?? null;
    }

    /**
     * @param $data
     * @return array
     */
    public function find($data): array
    {
        if(!is_object($data)) {
            return [];
        }

        $ids = [];

        if(property_exists($data, 'rider_id') && !is_null($data->rider_id)) {
            $ids['rider_id'] = ['id' => $data->rider_id, 'class' => Rider::class];
        }
        if(property_exists($data, 'user_id') && !is_null($data->user_id)) {
            $ids['user_id'] = ['id' => $data->user_id, 'class' => User::class];
        }
        if(property_exists($data, 'vendor_id') && !is_null($data->vendor_id)) {
            $ids['vendor_id'] = ['id' => $data->vendor_id, 'class' => Vendor::class];
        }
        if(property_exists($data, 'staff_id') && !is_null($data->staff_id)) {
            $ids['staff_id'] = ['id' => $data->staff_id, 'class' => Staff::class];
        }

        if(empty($ids)){
            return [];
        }

        $ids = array_filter($ids);

        $user = [];

        foreach ($ids as $key => $value) {
            if($value['class'] == Rider::class){
                $data = (new $value['class'])->withTrashed()->find($value['id']);
                if ($data) {
                    $user['id'] = $data->id;
                    $user['type'] = 'rider';
                    $user['code'] = $data->code;
                    $user['name'] = $data->first_name .' '.$data->last_name;
                    $user['avatar'] = $data->avatar;
                    $user['online'] = $data->online;
                }
            }
            if($value['class'] == Vendor::class){
                $data = (new $value['class'])->withTrashed()->find($value['id']);
                if ($data) {
                    $user['id'] = $data->id;
                    $user['type'] = 'vendor';
                    $user['code'] = $data->code;
                    $user['name'] = $data->business_name;
                    $user['avatar'] = $data->business_logo;
                    $user['online'] = $data->online;
                }
            }
            if($value['class'] == User::class){
                $data = (new $value['class'])->withTrashed()->find($value['id']);
                if ($data) {
                    $user['id'] = $data->id;
                    $user['type'] = 'customer';
                    $user['code'] = $data->code;
                    $user['name'] = $data->name;
                    $user['avatar'] = $data->avatar;
                    $user['online'] = $data->online;
                }
            }
            if($value['class'] == Staff::class){
                $data = (new $value['class'])->withTrashed()->find($value['id']);
                if ($data) {
                    $user['id'] = $data->id;
                    $user['type'] = 'staff';
                    $user['code'] = $data->code;
                    $user['name'] = $data->name;
                    $user['avatar'] = $data->avatar;
                    $user['online'] = $data->online;
                }
            }
        }

        return $user;
    }

    /**
     * @param $data
     * @return array
     */
    public static function getWhoId($data): array
    {
        $data = $data->toArray();
        $ids = [];

        if(isset($data['rider_id']) && !is_null($data['rider_id'])) {
            $ids['rider_id'] = ['id' => $data['rider_id'], 'class' => Rider::class];
        }
        if(isset($data['user_id']) && !is_null($data['user_id'])) {
            $ids['user_id'] = ['id' => $data['user_id'], 'class' => User::class];
        }
        if(isset($data['vendor_id']) && !is_null($data['vendor_id'])) {
            $ids['vendor_id'] = ['id' => $data['vendor_id'], 'class' => Vendor::class];
        }
        if(isset($data['staff_id']) && !is_null($data['staff_id'])) {
            $ids['staff_id'] = ['id' => $data['staff_id'], 'class' => Staff::class];
        }

        $ids = array_filter($ids);

        $user = [];

        foreach ($ids as $key => $value) {
            $data = (new $value['class'])->withTrashed()->find($value['id']);
            if ($data) {
                $user['id'] = $data->id;
                $user['type'] = static::getClassType($value['class']).'_id';
            }
        }

        return $user;
    }

    /**
     * @param $permission
     * @param array|string $key
     * @return mixed
     */
    public static function permissions($permission): mixed
    {
        $can = me()->permissions[$permission];
        if(!$can){
            return response()->json([RequestStatus::ERROR => $message ?? 'You do not have the permission to perform this action'], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        return true;
    }

    /**
     * @param $className
     * @return string
     */
    public static function getClassType($className): string
    {
        return strtolower(str_replace('App\\', '', str_replace('::class', '', $className)));
    }

    /**
     * @param $type
     * @param $id
     * @return array
     */
    public static function search($type, $id): array
    {
        $userType = static::getModelClass($type);

        $user = $userType::find($id);
        $res = QueryStatus::check_found($user, 'The selected user could not be found.');
        if($res){
            return [];
        }

        $data = [];

        $data['type'] = $type;
        $data['code'] = $user->code;
        if($user->first_name){
            $data['name'] = $user->first_name .' '.$user->last_name;
        }
        else{
            $data['name'] = $user->name;
        }
        $data['avatar'] = $user->avatar;
        $data['online'] = $user->online;

        return $data;
    }

    /**
     * @param $name
     * @return string
     */
    public function default_avatar($name): string
    {
        return "https://avatar.iran.liara.run/username?username=".$name;
    }
}
