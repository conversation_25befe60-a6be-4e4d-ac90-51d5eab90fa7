import { Badge } from "@/Components/ui/badge";
import { Button } from "@/Components/ui/button";
import { Card, CardContent, CardHeader } from "@/Components/ui/card";
import { Input } from "@/Components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/Components/ui/select";
import { Country } from "@/types/models/country";
import { State } from "@/types/models/location";
import { router } from "@inertiajs/react";
import * as Flags from "country-flag-icons/react/3x2";
import { ChevronLeft, Plus, Search } from "lucide-react";
import { useState } from "react";
import AddLocation from "./add-location";

type Props = {
    country: Country;
    states?: State[];
    hideBackButton?: boolean;
};

type LocationType = "state" | "city";

interface Location {
    id: number;
    name: string;
    type: LocationType;
    status: "active" | "suspended";
    created_at: string;
}

export default function ViewCountry({
    country,
    states = [],
    hideBackButton = false,
}: Props) {
    const [searchValue, setSearchValue] = useState("");
    const [statusFilter, setStatusFilter] = useState<string>("all");
    const [locationType, setLocationType] = useState<string>("all");

    const FlagComponent = Flags[country.iso as keyof typeof Flags];

    return (
        <div className="space-y-6">
            {/* Country Info Card */}
            <Card>
                <CardHeader className="pb-4">
                    <div className="flex items-center gap-4">
                        {FlagComponent && (
                            <FlagComponent
                                className="w-12 h-8"
                                title={country.name}
                            />
                        )}
                        <div>
                            <h2 className="text-2xl font-semibold">
                                {country.name}
                            </h2>
                            <div className="flex items-center gap-4 text-sm text-gray-500">
                                <span>ISO: {country.iso}</span>
                                <span>Currency: {country.currency}</span>
                                <span>Phone Code: {country.phonecode}</span>
                            </div>
                        </div>
                    </div>
                </CardHeader>
            </Card>

            {/* States & Cities Section */}
            <div className="flex items-center justify-between">
                <div>
                    <h3 className="text-lg font-semibold">States & Cities</h3>
                    <p className="text-gray-400 text-sm">
                        Manage locations under selected countries
                    </p>
                </div>

                <AddLocation country={country} states={states} />
            </div>
            <Card>
                <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                        <h3 className="text-lg font-semibold">
                            States & Cities
                        </h3>
                        <div className="flex items-center gap-3">
                            {/* Search Input */}
                            <div className="relative w-64">
                                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                                <Input
                                    placeholder="Search for state or city..."
                                    value={searchValue}
                                    onChange={(e) =>
                                        setSearchValue(e.target.value)
                                    }
                                    className="pl-8"
                                />
                            </div>

                            {/* Location Type Filter */}
                            <Select
                                value={locationType}
                                onValueChange={setLocationType}
                            >
                                <SelectTrigger className="w-32">
                                    <SelectValue placeholder="Location Type" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">
                                        All Types
                                    </SelectItem>
                                    <SelectItem value="state">State</SelectItem>
                                    <SelectItem value="city">City</SelectItem>
                                </SelectContent>
                            </Select>

                            {/* Status Filter */}
                            <Select
                                value={statusFilter}
                                onValueChange={setStatusFilter}
                            >
                                <SelectTrigger className="w-32">
                                    <SelectValue placeholder="Status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">
                                        All Status
                                    </SelectItem>
                                    <SelectItem value="active">
                                        Active
                                    </SelectItem>
                                    <SelectItem value="suspended">
                                        Suspended
                                    </SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                </CardHeader>

                <CardContent>
                    {/* Table Header */}
                    <div className="border-b pb-4 grid grid-cols-12 gap-4 text-sm font-medium text-gray-500">
                        <div className="col-span-4">Name of State</div>
                        <div className="col-span-3">Location Type</div>
                        <div className="col-span-3">Status</div>
                        <div className="col-span-2">Created Date</div>
                    </div>

                    {/* Table Content - Empty State */}
                    <div className="py-8 text-center text-gray-500">
                        No locations found. Add a new state or city to get
                        started.
                    </div>

                    {/* Table Content - With Data (commented out for now) */}
                    {/* <div className="divide-y">
                        {locations.map((location) => (
                            <div key={location.id} className="py-4 grid grid-cols-12 gap-4 items-center">
                                <div className="col-span-4">{location.name}</div>
                                <div className="col-span-3">
                                    <Badge variant="secondary" className="capitalize">
                                        {location.type}
                                    </Badge>
                                </div>
                                <div className="col-span-3">
                                    <Badge
                                        variant={location.status === 'active' ? 'default' : 'destructive'}
                                        className={location.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}
                                    >
                                        {location.status === 'active' ? 'Active' : 'Suspended'}
                                    </Badge>
                                </div>
                                <div className="col-span-2 text-sm text-gray-500">
                                    {new Date(location.created_at).toLocaleDateString()}
                                </div>
                            </div>
                        ))}
                    </div> */}
                </CardContent>
            </Card>
        </div>
    );
}
