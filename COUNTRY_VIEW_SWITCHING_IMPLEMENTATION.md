# Country View Switching Implementation

## Overview

This implementation adds a seamless view switching mechanism to the country management section, allowing users to toggle between a **Table View** (showing all countries in a paginated table) and a **Detail View** (showing detailed information for a selected country).

## Features Implemented

### 1. View Toggle Controls
- **Toggle Buttons**: Clean UI controls in the header that allow switching between views
- **Active State Indication**: Visual feedback showing which view is currently active
- **Disabled States**: Proper handling when no country is selected for detail view

### 2. State Management
- **View Mode State**: Tracks current view (`table` or `detail`)
- **Selected Country State**: Maintains the currently selected country for detail view
- **Loading States**: Smooth transitions with loading indicators during view switches

### 3. Navigation Patterns
- **"View Country" Action**: Clicking "View Country" in the dropdown switches to detail view
- **Back Navigation**: "Back to Countries" button returns to table view
- **Auto-Selection**: If switching to detail view with no country selected, automatically selects the first country

### 4. Smooth Transitions
- **CSS Animations**: Fade-in and slide animations for view transitions
- **Loading States**: Brief loading states to prevent jarring transitions
- **Preserved State**: Search terms and pagination state are maintained when switching views

## File Changes

### 1. Routes (`routes/web.php`)
```php
// Added route for viewing individual countries
Route::get('/settings/countries/{id}', [SettingsController::class, 'viewCountry'])->name('settings.countries.view');
```

### 2. Controller (`app/Http/Controllers/Dashboard/SettingsController.php`)
```php
// Added method to handle individual country viewing
public function viewCountry(Request $request, $id)
{
    try {
        $country = Country::withTrashed()->findOrFail($id);
        
        return Inertia::render($this->path.'/locations/view-country', [
            'country' => $country,
        ]);
    } catch (Exception $exception) {
        return (new ThrowException())->throw($exception, $request);
    }
}
```

### 3. Countries Table Component (`resources/js/Pages/settings/locations/countries.tsx`)
- Added `onViewCountry` prop to handle country selection
- Updated "View Country" dropdown item with click handler

### 4. View Country Component (`resources/js/Pages/settings/locations/view-country.tsx`)
- Added `hideBackButton` prop to conditionally hide the back button
- Made component more flexible for embedded use

### 5. Main Locations Component (`resources/js/Pages/settings/locations/index.tsx`)
- **View State Management**: Added state for current view mode and selected country
- **Toggle Controls**: Implemented view switching buttons with proper styling
- **Conditional Rendering**: Shows appropriate content based on current view
- **Smooth Transitions**: Added CSS animations for view transitions
- **Event Handlers**: Implemented handlers for view switching and country selection

## User Experience Features

### 1. Intuitive Controls
- Clear visual distinction between table and detail view buttons
- Breadcrumb-style navigation showing current country in detail view
- Consistent back navigation patterns

### 2. State Preservation
- Search terms are maintained when switching views
- Selected countries persist during view changes
- Pagination state is preserved in table view

### 3. Loading and Error Handling
- Smooth transitions prevent jarring view changes
- Disabled states prevent invalid actions
- Proper error handling for missing countries

### 4. Responsive Design
- View toggle buttons adapt to different screen sizes
- Consistent styling with existing application design
- Proper spacing and alignment

## Usage

### Switching to Detail View
1. **From Table**: Click "View Country" in any country's dropdown menu
2. **From Toggle**: Click "Detail View" button (auto-selects first country if none selected)

### Switching to Table View
1. **From Detail**: Click "Back to Countries" button
2. **From Toggle**: Click "Table View" button

### Navigation Flow
```
Table View → [View Country] → Detail View → [Back to Countries] → Table View
     ↑                                                                ↓
     ←←←←←←←←←←←←← [Table View Button] ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←
```

## Technical Implementation Details

### State Management
- Uses React hooks for local state management
- Implements proper cleanup and state transitions
- Handles edge cases (no countries, invalid selections)

### Performance Considerations
- Debounced search to prevent excessive API calls
- Efficient re-rendering with proper dependency arrays
- Minimal state updates during transitions

### Accessibility
- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader friendly transitions

## Future Enhancements

1. **URL State Management**: Sync view state with browser URL
2. **Keyboard Shortcuts**: Add hotkeys for quick view switching
3. **View Preferences**: Remember user's preferred view mode
4. **Enhanced Animations**: More sophisticated transition effects
5. **Mobile Optimization**: Improved mobile view switching experience
