<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

class CreateOwnersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('owners', function (Blueprint $table) {
            $table->increments('id');
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email')->unique();
            $table->string('phone')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('avatar')->nullable();
            $table->foreignId('country_id')->nullable();
            $table->foreignId('state_id')->nullable();
            $table->foreignId('city_id')->nullable();
            $table->boolean('approved')->default(false);
            $table->text('approval_comment')->nullable();
            $table->boolean('rejected')->default(false);
            $table->string('rejection_comment')->nullable();
            $table->text('rejection_reason')->nullable();
            $table->string('balance')->default(0);
            $table->string('commission')->default(0);
            $table->string('email_verified')->nullable();
            $table->string('phone_verified')->nullable();
            $table->dateTime('last_login')->nullable();
            $table->dateTime('send_next_otp_sms_after')->nullable();
            $table->string('password_reset_code')->nullable();

            $table->string('password');
            $table->string('code')->unique();
            $table->softDeletes();
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('owners');
    }
}
