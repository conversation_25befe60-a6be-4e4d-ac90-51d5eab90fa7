<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('owner_kycs', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('owner_id');
            $table->string('document_type');
            $table->string('number');
            $table->json('document')->nullable();
            $table->json('face')->nullable();
            $table->boolean('approved')->default(false);
            $table->text('approval_comment')->nullable();
            $table->boolean('rejected')->default(false);
            $table->string('rejection_comment')->nullable();
            $table->text('rejection_reason')->nullable();
            $table->string('status')->default('pending');
            $table->string('code')->unique();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('owner_kycs');
    }
};
