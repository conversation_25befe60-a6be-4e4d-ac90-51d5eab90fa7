<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_chat_id');
            $table->foreignId('sender_id');
            $table->string('sender_type');
            $table->text('message')->nullable();
            $table->json('images')->nullable();
            $table->json('video')->nullable();
            $table->text('voice_note')->nullable();
            $table->boolean('read')->default(false);
            $table->boolean('reply')->default(false);
            $table->foreignId('order_chat_message_id')->nullable();
            $table->string('code')->unique();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_messages');
    }
};
