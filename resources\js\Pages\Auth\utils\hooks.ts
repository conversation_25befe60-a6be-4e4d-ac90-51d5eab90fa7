import { useMutation, useQueryClient } from '@tanstack/react-query'
import axios, { AxiosError } from 'axios'
import { toast } from 'sonner'

export const useLogin = () => {
    const queryClient = useQueryClient()
    

    return useMutation({
        mutationFn: (credentials: { email: string, password: string }) => {
            return axios.post(route('login'), credentials)
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['auth'] })
        },
        onError: (error) => {
            if (error instanceof AxiosError) {
                toast.error(error.response?.data.message, { position: 'top-center' })
            } else {
                toast.error('Something went wrong')
            }
        }
    })
}